lockfileVersion: 5.4

specifiers:
  '@electron/remote': ^2.1.2
  '@emotion/react': ^11.11.3
  '@types/node': ^20.14.9
  '@types/react': ^18.2.43
  '@types/react-dom': ^18.2.17
  '@typescript-eslint/eslint-plugin': ^6.14.0
  '@typescript-eslint/parser': ^6.14.0
  '@vitejs/plugin-react-swc': ^3.5.0
  ahooks: ^3.8.0
  axios: ^1.8.4
  browser-image-compression: ^2.0.2
  cos-js-sdk-v5: ^1.8.1
  cos-nodejs-sdk-v5: ^2.14.7
  electron: ^30.1.0
  electron-builder: ^24.9.1
  eslint: ^8.55.0
  eslint-plugin-react-hooks: ^4.6.0
  eslint-plugin-react-refresh: ^0.4.5
  fast-glob: ^3.3.2
  filesize: ^10.1.2
  react: ^18.2.0
  react-dom: ^18.2.0
  semver: ^7.6.3
  tdesign-icons-react: ^0.3.2
  tdesign-react: ^1.7.6
  typescript: ^5.2.2
  vite: ^5.4.15
  vite-plugin-dts: ^4.4.0
  vite-plugin-electron: ^0.28.7
  vite-plugin-no-bundle: ^4.0.0

dependencies:
  '@electron/remote': mirrors.tencent.com/@electron/remote/2.1.2_electron@30.5.1
  axios: mirrors.tencent.com/axios/1.9.0
  cos-nodejs-sdk-v5: mirrors.tencent.com/cos-nodejs-sdk-v5/2.15.1
  electron: mirrors.tencent.com/electron/30.5.1

devDependencies:
  '@emotion/react': mirrors.tencent.com/@emotion/react/11.14.0_slmofdmvhg5ve3dprq2qc77rkq
  '@types/node': mirrors.tencent.com/@types/node/20.19.0
  '@types/react': mirrors.tencent.com/@types/react/18.3.23
  '@types/react-dom': mirrors.tencent.com/@types/react-dom/18.3.7_@types+react@18.3.23
  '@typescript-eslint/eslint-plugin': mirrors.tencent.com/@typescript-eslint/eslint-plugin/6.21.0_kuceqbxaaku7xpinkil3t6nsce
  '@typescript-eslint/parser': mirrors.tencent.com/@typescript-eslint/parser/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu
  '@vitejs/plugin-react-swc': mirrors.tencent.com/@vitejs/plugin-react-swc/3.10.2_vite@5.4.19
  ahooks: mirrors.tencent.com/ahooks/3.8.5_react@18.3.1
  browser-image-compression: mirrors.tencent.com/browser-image-compression/2.0.2
  cos-js-sdk-v5: mirrors.tencent.com/cos-js-sdk-v5/1.10.1
  electron-builder: mirrors.tencent.com/electron-builder/24.13.3
  eslint: mirrors.tencent.com/eslint/8.57.1
  eslint-plugin-react-hooks: mirrors.tencent.com/eslint-plugin-react-hooks/4.6.2_eslint@8.57.1
  eslint-plugin-react-refresh: mirrors.tencent.com/eslint-plugin-react-refresh/0.4.20_eslint@8.57.1
  fast-glob: mirrors.tencent.com/fast-glob/3.3.3
  filesize: mirrors.tencent.com/filesize/10.1.6
  react: mirrors.tencent.com/react/18.3.1
  react-dom: mirrors.tencent.com/react-dom/18.3.1_react@18.3.1
  semver: mirrors.tencent.com/semver/7.7.2
  tdesign-icons-react: mirrors.tencent.com/tdesign-icons-react/0.3.5_nnrd3gsncyragczmpvfhocinkq
  tdesign-react: mirrors.tencent.com/tdesign-react/1.12.2_nnrd3gsncyragczmpvfhocinkq
  typescript: mirrors.tencent.com/typescript/5.8.3
  vite: mirrors.tencent.com/vite/5.4.19_@types+node@20.19.0
  vite-plugin-dts: mirrors.tencent.com/vite-plugin-dts/4.5.4_q4274suvdejehmyjefaye234ii
  vite-plugin-electron: mirrors.tencent.com/vite-plugin-electron/0.28.8
  vite-plugin-no-bundle: mirrors.tencent.com/vite-plugin-no-bundle/4.0.0

packages:

  mirrors.tencent.com/7zip-bin/5.2.0:
    resolution: {integrity: sha512-ukTPVhqG4jNzMro2qA9HSCSSVJN3aN7tlb+hfqYCt3ER0yWroeA2VR38MNrOHLQ/cVj+DaIMad0kFCtWWowh/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/7zip-bin/-/7zip-bin-5.2.0.tgz}
    name: 7zip-bin
    version: 5.2.0
    dev: true

  mirrors.tencent.com/@babel/code-frame/7.27.1:
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/code-frame/-/code-frame-7.27.1.tgz}
    name: '@babel/code-frame'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': mirrors.tencent.com/@babel/helper-validator-identifier/7.27.1
      js-tokens: mirrors.tencent.com/js-tokens/4.0.0
      picocolors: mirrors.tencent.com/picocolors/1.1.1
    dev: true

  mirrors.tencent.com/@babel/generator/7.27.5:
    resolution: {integrity: sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/generator/-/generator-7.27.5.tgz}
    name: '@babel/generator'
    version: 7.27.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': mirrors.tencent.com/@babel/parser/7.27.5
      '@babel/types': mirrors.tencent.com/@babel/types/7.27.6
      '@jridgewell/gen-mapping': mirrors.tencent.com/@jridgewell/gen-mapping/0.3.8
      '@jridgewell/trace-mapping': mirrors.tencent.com/@jridgewell/trace-mapping/0.3.25
      jsesc: mirrors.tencent.com/jsesc/3.1.0
    dev: true

  mirrors.tencent.com/@babel/helper-module-imports/7.27.1:
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz}
    name: '@babel/helper-module-imports'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': mirrors.tencent.com/@babel/traverse/7.27.4
      '@babel/types': mirrors.tencent.com/@babel/types/7.27.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@babel/helper-string-parser/7.27.1:
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  mirrors.tencent.com/@babel/helper-validator-identifier/7.27.1:
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.27.1
    engines: {node: '>=6.9.0'}
    dev: true

  mirrors.tencent.com/@babel/parser/7.27.5:
    resolution: {integrity: sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/parser/-/parser-7.27.5.tgz}
    name: '@babel/parser'
    version: 7.27.5
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': mirrors.tencent.com/@babel/types/7.27.6
    dev: true

  mirrors.tencent.com/@babel/runtime/7.26.10:
    resolution: {integrity: sha512-2WJMeRQPHKSPemqk/awGrAiuFfzBmOIPXKizAsVhWH9YJqLZ0H+HS4c8loHGgW6utJ3E/ejXQUsiGaQy2NZ9Fw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.26.10.tgz}
    name: '@babel/runtime'
    version: 7.26.10
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: mirrors.tencent.com/regenerator-runtime/0.14.1
    dev: true

  mirrors.tencent.com/@babel/runtime/7.27.6:
    resolution: {integrity: sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/runtime/-/runtime-7.27.6.tgz}
    name: '@babel/runtime'
    version: 7.27.6
    engines: {node: '>=6.9.0'}
    dev: true

  mirrors.tencent.com/@babel/template/7.27.2:
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/template/-/template-7.27.2.tgz}
    name: '@babel/template'
    version: 7.27.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': mirrors.tencent.com/@babel/code-frame/7.27.1
      '@babel/parser': mirrors.tencent.com/@babel/parser/7.27.5
      '@babel/types': mirrors.tencent.com/@babel/types/7.27.6
    dev: true

  mirrors.tencent.com/@babel/traverse/7.27.4:
    resolution: {integrity: sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/traverse/-/traverse-7.27.4.tgz}
    name: '@babel/traverse'
    version: 7.27.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': mirrors.tencent.com/@babel/code-frame/7.27.1
      '@babel/generator': mirrors.tencent.com/@babel/generator/7.27.5
      '@babel/parser': mirrors.tencent.com/@babel/parser/7.27.5
      '@babel/template': mirrors.tencent.com/@babel/template/7.27.2
      '@babel/types': mirrors.tencent.com/@babel/types/7.27.6
      debug: mirrors.tencent.com/debug/4.4.1
      globals: mirrors.tencent.com/globals/11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@babel/types/7.27.6:
    resolution: {integrity: sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@babel/types/-/types-7.27.6.tgz}
    name: '@babel/types'
    version: 7.27.6
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': mirrors.tencent.com/@babel/helper-string-parser/7.27.1
      '@babel/helper-validator-identifier': mirrors.tencent.com/@babel/helper-validator-identifier/7.27.1
    dev: true

  mirrors.tencent.com/@develar/schema-utils/2.6.5:
    resolution: {integrity: sha512-0cp4PsWQ/9avqTVMCtZ+GirikIA36ikvjtHweU4/j8yLtgObI0+JUPhYFScgwlteveGB1rt3Cm8UhN04XayDig==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@develar/schema-utils/-/schema-utils-2.6.5.tgz}
    name: '@develar/schema-utils'
    version: 2.6.5
    engines: {node: '>= 8.9.0'}
    dependencies:
      ajv: mirrors.tencent.com/ajv/6.12.6
      ajv-keywords: mirrors.tencent.com/ajv-keywords/3.5.2_ajv@6.12.6
    dev: true

  mirrors.tencent.com/@electron/asar/3.4.1:
    resolution: {integrity: sha512-i4/rNPRS84t0vSRa2HorerGRXWyF4vThfHesw0dmcWHp+cspK743UanA0suA5Q5y8kzY2y6YKrvbIUn69BCAiA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/asar/-/asar-3.4.1.tgz}
    name: '@electron/asar'
    version: 3.4.1
    engines: {node: '>=10.12.0'}
    hasBin: true
    dependencies:
      commander: mirrors.tencent.com/commander/5.1.0
      glob: mirrors.tencent.com/glob/7.2.3
      minimatch: mirrors.tencent.com/minimatch/3.1.2
    dev: true

  mirrors.tencent.com/@electron/get/2.0.3:
    resolution: {integrity: sha512-Qkzpg2s9GnVV2I2BjRksUi43U5e6+zaQMcjoJy0C+C5oxaKl+fmckGDQFtRpZpZV0NQekuZZ+tGz7EA9TVnQtQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/get/-/get-2.0.3.tgz}
    name: '@electron/get'
    version: 2.0.3
    engines: {node: '>=12'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
      env-paths: mirrors.tencent.com/env-paths/2.2.1
      fs-extra: mirrors.tencent.com/fs-extra/8.1.0
      got: mirrors.tencent.com/got/11.8.6
      progress: mirrors.tencent.com/progress/2.0.3
      semver: mirrors.tencent.com/semver/6.3.1
      sumchecker: mirrors.tencent.com/sumchecker/3.0.1
    optionalDependencies:
      global-agent: mirrors.tencent.com/global-agent/3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  mirrors.tencent.com/@electron/notarize/2.2.1:
    resolution: {integrity: sha512-aL+bFMIkpR0cmmj5Zgy0LMKEpgy43/hw5zadEArgmAMWWlKc5buwFvFT9G/o/YJkvXAJm5q3iuTuLaiaXW39sg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/notarize/-/notarize-2.2.1.tgz}
    name: '@electron/notarize'
    version: 2.2.1
    engines: {node: '>= 10.0.0'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
      fs-extra: mirrors.tencent.com/fs-extra/9.1.0
      promise-retry: mirrors.tencent.com/promise-retry/2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@electron/osx-sign/1.0.5:
    resolution: {integrity: sha512-k9ZzUQtamSoweGQDV2jILiRIHUu7lYlJ3c6IEmjv1hC17rclE+eb9U+f6UFlOOETo0JzY1HNlXy4YOlCvl+Lww==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/osx-sign/-/osx-sign-1.0.5.tgz}
    name: '@electron/osx-sign'
    version: 1.0.5
    engines: {node: '>=12.0.0'}
    hasBin: true
    dependencies:
      compare-version: mirrors.tencent.com/compare-version/0.1.2
      debug: mirrors.tencent.com/debug/4.4.1
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      isbinaryfile: mirrors.tencent.com/isbinaryfile/4.0.10
      minimist: mirrors.tencent.com/minimist/1.2.8
      plist: mirrors.tencent.com/plist/3.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@electron/remote/2.1.2_electron@30.5.1:
    resolution: {integrity: sha512-EPwNx+nhdrTBxyCqXt/pftoQg/ybtWDW3DUWHafejvnB1ZGGfMpv6e15D8KeempocjXe78T7WreyGGb3mlZxdA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/remote/-/remote-2.1.2.tgz}
    id: mirrors.tencent.com/@electron/remote/2.1.2
    name: '@electron/remote'
    version: 2.1.2
    peerDependencies:
      electron: '>= 13.0.0'
    dependencies:
      electron: mirrors.tencent.com/electron/30.5.1
    dev: false

  mirrors.tencent.com/@electron/universal/1.5.1:
    resolution: {integrity: sha512-kbgXxyEauPJiQQUNG2VgUeyfQNFk6hBF11ISN2PNI6agUgPl55pv4eQmaqHzTAzchBvqZ2tQuRVaPStGf0mxGw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@electron/universal/-/universal-1.5.1.tgz}
    name: '@electron/universal'
    version: 1.5.1
    engines: {node: '>=8.6'}
    dependencies:
      '@electron/asar': mirrors.tencent.com/@electron/asar/3.4.1
      '@malept/cross-spawn-promise': mirrors.tencent.com/@malept/cross-spawn-promise/1.1.1
      debug: mirrors.tencent.com/debug/4.4.1
      dir-compare: mirrors.tencent.com/dir-compare/3.3.0
      fs-extra: mirrors.tencent.com/fs-extra/9.1.0
      minimatch: mirrors.tencent.com/minimatch/3.1.2
      plist: mirrors.tencent.com/plist/3.1.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@emotion/babel-plugin/11.13.5:
    resolution: {integrity: sha512-pxHCpT2ex+0q+HH91/zsdHkw/lXd468DIN2zvfvLtPKLLMo6gQj7oLObq8PhkrxOZb/gGCq03S3Z7PDhS8pduQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz}
    name: '@emotion/babel-plugin'
    version: 11.13.5
    dependencies:
      '@babel/helper-module-imports': mirrors.tencent.com/@babel/helper-module-imports/7.27.1
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.27.6
      '@emotion/hash': mirrors.tencent.com/@emotion/hash/0.9.2
      '@emotion/memoize': mirrors.tencent.com/@emotion/memoize/0.9.0
      '@emotion/serialize': mirrors.tencent.com/@emotion/serialize/1.3.3
      babel-plugin-macros: mirrors.tencent.com/babel-plugin-macros/3.1.0
      convert-source-map: mirrors.tencent.com/convert-source-map/1.9.0
      escape-string-regexp: mirrors.tencent.com/escape-string-regexp/4.0.0
      find-root: mirrors.tencent.com/find-root/1.1.0
      source-map: mirrors.tencent.com/source-map/0.5.7
      stylis: mirrors.tencent.com/stylis/4.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@emotion/cache/11.14.0:
    resolution: {integrity: sha512-L/B1lc/TViYk4DcpGxtAVbx0ZyiKM5ktoIyafGkH6zg/tj+mA+NE//aPYKG0k8kCHSHVJrpLpcAlOBEXQ3SavA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/cache/-/cache-11.14.0.tgz}
    name: '@emotion/cache'
    version: 11.14.0
    dependencies:
      '@emotion/memoize': mirrors.tencent.com/@emotion/memoize/0.9.0
      '@emotion/sheet': mirrors.tencent.com/@emotion/sheet/1.4.0
      '@emotion/utils': mirrors.tencent.com/@emotion/utils/1.4.2
      '@emotion/weak-memoize': mirrors.tencent.com/@emotion/weak-memoize/0.4.0
      stylis: mirrors.tencent.com/stylis/4.2.0
    dev: true

  mirrors.tencent.com/@emotion/hash/0.9.2:
    resolution: {integrity: sha512-MyqliTZGuOm3+5ZRSaaBGP3USLw6+EGykkwZns2EPC5g8jJ4z9OrdZY9apkl3+UP9+sdz76YYkwCKP5gh8iY3g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/hash/-/hash-0.9.2.tgz}
    name: '@emotion/hash'
    version: 0.9.2
    dev: true

  mirrors.tencent.com/@emotion/memoize/0.9.0:
    resolution: {integrity: sha512-30FAj7/EoJ5mwVPOWhAyCX+FPfMDrVecJAM+Iw9NRoSl4BBAQeqj4cApHHUXOVvIPgLVDsCFoz/hGD+5QQD1GQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/memoize/-/memoize-0.9.0.tgz}
    name: '@emotion/memoize'
    version: 0.9.0
    dev: true

  mirrors.tencent.com/@emotion/react/11.14.0_slmofdmvhg5ve3dprq2qc77rkq:
    resolution: {integrity: sha512-O000MLDBDdk/EohJPFUqvnp4qnHeYkVP5B0xEG0D/L7cOKP9kefu2DXn8dj74cQfsEzUqh+sr1RzFqiL1o+PpA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/react/-/react-11.14.0.tgz}
    id: mirrors.tencent.com/@emotion/react/11.14.0
    name: '@emotion/react'
    version: 11.14.0
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.27.6
      '@emotion/babel-plugin': mirrors.tencent.com/@emotion/babel-plugin/11.13.5
      '@emotion/cache': mirrors.tencent.com/@emotion/cache/11.14.0
      '@emotion/serialize': mirrors.tencent.com/@emotion/serialize/1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': mirrors.tencent.com/@emotion/use-insertion-effect-with-fallbacks/1.2.0_react@18.3.1
      '@emotion/utils': mirrors.tencent.com/@emotion/utils/1.4.2
      '@emotion/weak-memoize': mirrors.tencent.com/@emotion/weak-memoize/0.4.0
      '@types/react': mirrors.tencent.com/@types/react/18.3.23
      hoist-non-react-statics: mirrors.tencent.com/hoist-non-react-statics/3.3.2
      react: mirrors.tencent.com/react/18.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@emotion/serialize/1.3.3:
    resolution: {integrity: sha512-EISGqt7sSNWHGI76hC7x1CksiXPahbxEOrC5RjmFRJTqLyEK9/9hZvBbiYn70dw4wuwMKiEMCUlR6ZXTSWQqxA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/serialize/-/serialize-1.3.3.tgz}
    name: '@emotion/serialize'
    version: 1.3.3
    dependencies:
      '@emotion/hash': mirrors.tencent.com/@emotion/hash/0.9.2
      '@emotion/memoize': mirrors.tencent.com/@emotion/memoize/0.9.0
      '@emotion/unitless': mirrors.tencent.com/@emotion/unitless/0.10.0
      '@emotion/utils': mirrors.tencent.com/@emotion/utils/1.4.2
      csstype: mirrors.tencent.com/csstype/3.1.3
    dev: true

  mirrors.tencent.com/@emotion/sheet/1.4.0:
    resolution: {integrity: sha512-fTBW9/8r2w3dXWYM4HCB1Rdp8NLibOw2+XELH5m5+AkWiL/KqYX6dc0kKYlaYyKjrQ6ds33MCdMPEwgs2z1rqg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/sheet/-/sheet-1.4.0.tgz}
    name: '@emotion/sheet'
    version: 1.4.0
    dev: true

  mirrors.tencent.com/@emotion/unitless/0.10.0:
    resolution: {integrity: sha512-dFoMUuQA20zvtVTuxZww6OHoJYgrzfKM1t52mVySDJnMSEa08ruEvdYQbhvyu6soU+NeLVd3yKfTfT0NeV6qGg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/unitless/-/unitless-0.10.0.tgz}
    name: '@emotion/unitless'
    version: 0.10.0
    dev: true

  mirrors.tencent.com/@emotion/use-insertion-effect-with-fallbacks/1.2.0_react@18.3.1:
    resolution: {integrity: sha512-yJMtVdH59sxi/aVJBpk9FQq+OR8ll5GT8oWd57UpeaKEVGab41JWaCFA7FRLoMLloOZF/c/wsPoe+bfGmRKgDg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz}
    id: mirrors.tencent.com/@emotion/use-insertion-effect-with-fallbacks/1.2.0
    name: '@emotion/use-insertion-effect-with-fallbacks'
    version: 1.2.0
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: mirrors.tencent.com/react/18.3.1
    dev: true

  mirrors.tencent.com/@emotion/utils/1.4.2:
    resolution: {integrity: sha512-3vLclRofFziIa3J2wDh9jjbkUz9qk5Vi3IZ/FSTKViB0k+ef0fPV7dYrUIugbgupYDx7v9ud/SjrtEP8Y4xLoA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/utils/-/utils-1.4.2.tgz}
    name: '@emotion/utils'
    version: 1.4.2
    dev: true

  mirrors.tencent.com/@emotion/weak-memoize/0.4.0:
    resolution: {integrity: sha512-snKqtPW01tN0ui7yu9rGv69aJXr/a/Ywvl11sUjNtEcRc+ng/mQriFL0wLXMef74iHa/EkftbDzU9F8iFbH+zg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz}
    name: '@emotion/weak-memoize'
    version: 0.4.0
    dev: true

  mirrors.tencent.com/@esbuild/aix-ppc64/0.21.5:
    resolution: {integrity: sha512-1SDgH6ZSPTlggy1yI6+Dbkiz8xzpHJEVAlF/AM1tHPLsf5STom9rwtjE4hKAF20FfXXNTFqEYXyJNWh1GiZedQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz}
    name: '@esbuild/aix-ppc64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [aix]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/android-arm/0.21.5:
    resolution: {integrity: sha512-vCPvzSjpPHEi1siZdlvAlsPxXl7WbOVUBBAowWug4rJHb68Ox8KualB+1ocNvT5fjv6wpkX6o/iEpbDrf68zcg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/android-arm/-/android-arm-0.21.5.tgz}
    name: '@esbuild/android-arm'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/android-arm64/0.21.5:
    resolution: {integrity: sha512-c0uX9VAUBQ7dTDCjq+wdyGLowMdtR/GoC2U5IYk/7D1H1JYC0qseD7+11iMP2mRLN9RcCMRcjC4YMclCzGwS/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz}
    name: '@esbuild/android-arm64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/android-x64/0.21.5:
    resolution: {integrity: sha512-D7aPRUUNHRBwHxzxRvp856rjUHRFW1SdQATKXH2hqA0kAZb1hKmi02OpYRacl0TxIGz/ZmXWlbZgjwWYaCakTA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/android-x64/-/android-x64-0.21.5.tgz}
    name: '@esbuild/android-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/darwin-arm64/0.21.5:
    resolution: {integrity: sha512-DwqXqZyuk5AiWWf3UfLiRDJ5EDd49zg6O9wclZ7kUMv2WRFr4HKjXp/5t8JZ11QbQfUS6/cRCKGwYhtNAY88kQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz}
    name: '@esbuild/darwin-arm64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/darwin-x64/0.21.5:
    resolution: {integrity: sha512-se/JjF8NlmKVG4kNIuyWMV/22ZaerB+qaSi5MdrXtd6R08kvs2qCN4C09miupktDitvh8jRFflwGFBQcxZRjbw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz}
    name: '@esbuild/darwin-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/freebsd-arm64/0.21.5:
    resolution: {integrity: sha512-5JcRxxRDUJLX8JXp/wcBCy3pENnCgBR9bN6JsY4OmhfUtIHe3ZW0mawA7+RDAcMLrMIZaf03NlQiX9DGyB8h4g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz}
    name: '@esbuild/freebsd-arm64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/freebsd-x64/0.21.5:
    resolution: {integrity: sha512-J95kNBj1zkbMXtHVH29bBriQygMXqoVQOQYA+ISs0/2l3T9/kj42ow2mpqerRBxDJnmkUDCaQT/dfNXWX/ZZCQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz}
    name: '@esbuild/freebsd-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-arm/0.21.5:
    resolution: {integrity: sha512-bPb5AHZtbeNGjCKVZ9UGqGwo8EUu4cLq68E95A53KlxAPRmUyYv2D6F0uUI65XisGOL1hBP5mTronbgo+0bFcA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz}
    name: '@esbuild/linux-arm'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-arm64/0.21.5:
    resolution: {integrity: sha512-ibKvmyYzKsBeX8d8I7MH/TMfWDXBF3db4qM6sy+7re0YXya+K1cem3on9XgdT2EQGMu4hQyZhan7TeQ8XkGp4Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz}
    name: '@esbuild/linux-arm64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-ia32/0.21.5:
    resolution: {integrity: sha512-YvjXDqLRqPDl2dvRODYmmhz4rPeVKYvppfGYKSNGdyZkA01046pLWyRKKI3ax8fbJoK5QbxblURkwK/MWY18Tg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz}
    name: '@esbuild/linux-ia32'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-loong64/0.21.5:
    resolution: {integrity: sha512-uHf1BmMG8qEvzdrzAqg2SIG/02+4/DHB6a9Kbya0XDvwDEKCoC8ZRWI5JJvNdUjtciBGFQ5PuBlpEOXQj+JQSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz}
    name: '@esbuild/linux-loong64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-mips64el/0.21.5:
    resolution: {integrity: sha512-IajOmO+KJK23bj52dFSNCMsz1QP1DqM6cwLUv3W1QwyxkyIWecfafnI555fvSGqEKwjMXVLokcV5ygHW5b3Jbg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz}
    name: '@esbuild/linux-mips64el'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-ppc64/0.21.5:
    resolution: {integrity: sha512-1hHV/Z4OEfMwpLO8rp7CvlhBDnjsC3CttJXIhBi+5Aj5r+MBvy4egg7wCbe//hSsT+RvDAG7s81tAvpL2XAE4w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz}
    name: '@esbuild/linux-ppc64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-riscv64/0.21.5:
    resolution: {integrity: sha512-2HdXDMd9GMgTGrPWnJzP2ALSokE/0O5HhTUvWIbD3YdjME8JwvSCnNGBnTThKGEB91OZhzrJ4qIIxk/SBmyDDA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz}
    name: '@esbuild/linux-riscv64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-s390x/0.21.5:
    resolution: {integrity: sha512-zus5sxzqBJD3eXxwvjN1yQkRepANgxE9lgOW2qLnmr8ikMTphkjgXu1HR01K4FJg8h1kEEDAqDcZQtbrRnB41A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz}
    name: '@esbuild/linux-s390x'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/linux-x64/0.21.5:
    resolution: {integrity: sha512-1rYdTpyv03iycF1+BhzrzQJCdOuAOtaqHTWJZCWvijKD2N5Xu0TtVC8/+1faWqcP9iBCWOmjmhoH94dH82BxPQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz}
    name: '@esbuild/linux-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/netbsd-x64/0.21.5:
    resolution: {integrity: sha512-Woi2MXzXjMULccIwMnLciyZH4nCIMpWQAs049KEeMvOcNADVxo0UBIQPfSmxB3CWKedngg7sWZdLvLczpe0tLg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz}
    name: '@esbuild/netbsd-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/openbsd-x64/0.21.5:
    resolution: {integrity: sha512-HLNNw99xsvx12lFBUwoT8EVCsSvRNDVxNpjZ7bPn947b8gJPzeHWyNVhFsaerc0n3TsbOINvRP2byTZ5LKezow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz}
    name: '@esbuild/openbsd-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/sunos-x64/0.21.5:
    resolution: {integrity: sha512-6+gjmFpfy0BHU5Tpptkuh8+uw3mnrvgs+dSPQXQOv3ekbordwnzTVEb4qnIvQcYXq6gzkyTnoZ9dZG+D4garKg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz}
    name: '@esbuild/sunos-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/win32-arm64/0.21.5:
    resolution: {integrity: sha512-Z0gOTd75VvXqyq7nsl93zwahcTROgqvuAcYDUr+vOv8uHhNSKROyU961kgtCD1e95IqPKSQKH7tBTslnS3tA8A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz}
    name: '@esbuild/win32-arm64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/win32-ia32/0.21.5:
    resolution: {integrity: sha512-SWXFF1CL2RVNMaVs+BBClwtfZSvDgtL//G/smwAc5oVK/UPu2Gu9tIaRgFmYFFKrmg3SyAjSrElf0TiJ1v8fYA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz}
    name: '@esbuild/win32-ia32'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@esbuild/win32-x64/0.21.5:
    resolution: {integrity: sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz}
    name: '@esbuild/win32-x64'
    version: 0.21.5
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@eslint-community/eslint-utils/4.7.0_eslint@8.57.1:
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz}
    id: mirrors.tencent.com/@eslint-community/eslint-utils/4.7.0
    name: '@eslint-community/eslint-utils'
    version: 4.7.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: mirrors.tencent.com/eslint/8.57.1
      eslint-visitor-keys: mirrors.tencent.com/eslint-visitor-keys/3.4.3
    dev: true

  mirrors.tencent.com/@eslint-community/regexpp/4.12.1:
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@eslint-community/regexpp/-/regexpp-4.12.1.tgz}
    name: '@eslint-community/regexpp'
    version: 4.12.1
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  mirrors.tencent.com/@eslint/eslintrc/2.1.4:
    resolution: {integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@eslint/eslintrc/-/eslintrc-2.1.4.tgz}
    name: '@eslint/eslintrc'
    version: 2.1.4
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      ajv: mirrors.tencent.com/ajv/6.12.6
      debug: mirrors.tencent.com/debug/4.4.1
      espree: mirrors.tencent.com/espree/9.6.1
      globals: mirrors.tencent.com/globals/13.24.0
      ignore: mirrors.tencent.com/ignore/5.3.2
      import-fresh: mirrors.tencent.com/import-fresh/3.3.1
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
      minimatch: mirrors.tencent.com/minimatch/3.1.2
      strip-json-comments: mirrors.tencent.com/strip-json-comments/3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@eslint/js/8.57.1:
    resolution: {integrity: sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@eslint/js/-/js-8.57.1.tgz}
    name: '@eslint/js'
    version: 8.57.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  mirrors.tencent.com/@humanwhocodes/config-array/0.13.0:
    resolution: {integrity: sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@humanwhocodes/config-array/-/config-array-0.13.0.tgz}
    name: '@humanwhocodes/config-array'
    version: 0.13.0
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead
    dependencies:
      '@humanwhocodes/object-schema': mirrors.tencent.com/@humanwhocodes/object-schema/2.0.3
      debug: mirrors.tencent.com/debug/4.4.1
      minimatch: mirrors.tencent.com/minimatch/3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@humanwhocodes/module-importer/1.0.1:
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz}
    name: '@humanwhocodes/module-importer'
    version: 1.0.1
    engines: {node: '>=12.22'}
    dev: true

  mirrors.tencent.com/@humanwhocodes/object-schema/2.0.3:
    resolution: {integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz}
    name: '@humanwhocodes/object-schema'
    version: 2.0.3
    deprecated: Use @eslint/object-schema instead
    dev: true

  mirrors.tencent.com/@isaacs/cliui/8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@isaacs/cliui/-/cliui-8.0.2.tgz}
    name: '@isaacs/cliui'
    version: 8.0.2
    engines: {node: '>=12'}
    dependencies:
      string-width: mirrors.tencent.com/string-width/5.1.2
      string-width-cjs: mirrors.tencent.com/string-width/4.2.3
      strip-ansi: mirrors.tencent.com/strip-ansi/7.1.0
      strip-ansi-cjs: mirrors.tencent.com/strip-ansi/6.0.1
      wrap-ansi: mirrors.tencent.com/wrap-ansi/8.1.0
      wrap-ansi-cjs: mirrors.tencent.com/wrap-ansi/7.0.0
    dev: true

  mirrors.tencent.com/@jridgewell/gen-mapping/0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.8
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': mirrors.tencent.com/@jridgewell/set-array/1.2.1
      '@jridgewell/sourcemap-codec': mirrors.tencent.com/@jridgewell/sourcemap-codec/1.5.0
      '@jridgewell/trace-mapping': mirrors.tencent.com/@jridgewell/trace-mapping/0.3.25
    dev: true

  mirrors.tencent.com/@jridgewell/resolve-uri/3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.2
    engines: {node: '>=6.0.0'}
    dev: true

  mirrors.tencent.com/@jridgewell/set-array/1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    name: '@jridgewell/set-array'
    version: 1.2.1
    engines: {node: '>=6.0.0'}
    dev: true

  mirrors.tencent.com/@jridgewell/sourcemap-codec/1.5.0:
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.5.0
    dev: true

  mirrors.tencent.com/@jridgewell/trace-mapping/0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.25
    dependencies:
      '@jridgewell/resolve-uri': mirrors.tencent.com/@jridgewell/resolve-uri/3.1.2
      '@jridgewell/sourcemap-codec': mirrors.tencent.com/@jridgewell/sourcemap-codec/1.5.0
    dev: true

  mirrors.tencent.com/@malept/cross-spawn-promise/1.1.1:
    resolution: {integrity: sha512-RTBGWL5FWQcg9orDOCcp4LvItNzUPcyEU9bwaeJX0rJ1IQxzucC48Y0/sQLp/g6t99IQgAlGIaesJS+gTn7tVQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@malept/cross-spawn-promise/-/cross-spawn-promise-1.1.1.tgz}
    name: '@malept/cross-spawn-promise'
    version: 1.1.1
    engines: {node: '>= 10'}
    dependencies:
      cross-spawn: mirrors.tencent.com/cross-spawn/7.0.6
    dev: true

  mirrors.tencent.com/@malept/flatpak-bundler/0.4.0:
    resolution: {integrity: sha512-9QOtNffcOF/c1seMCDnjckb3R9WHcG34tky+FHpNKKCW0wc/scYLwMtO+ptyGUfMW0/b/n4qRiALlaFHc9Oj7Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@malept/flatpak-bundler/-/flatpak-bundler-0.4.0.tgz}
    name: '@malept/flatpak-bundler'
    version: 0.4.0
    engines: {node: '>= 10.0.0'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
      fs-extra: mirrors.tencent.com/fs-extra/9.1.0
      lodash: mirrors.tencent.com/lodash/4.17.21
      tmp-promise: mirrors.tencent.com/tmp-promise/3.0.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@microsoft/api-extractor-model/7.30.6_@types+node@20.19.0:
    resolution: {integrity: sha512-znmFn69wf/AIrwHya3fxX6uB5etSIn6vg4Q4RB/tb5VDDs1rqREc+AvMC/p19MUN13CZ7+V/8pkYPTj7q8tftg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@microsoft/api-extractor-model/-/api-extractor-model-7.30.6.tgz}
    id: mirrors.tencent.com/@microsoft/api-extractor-model/7.30.6
    name: '@microsoft/api-extractor-model'
    version: 7.30.6
    dependencies:
      '@microsoft/tsdoc': mirrors.tencent.com/@microsoft/tsdoc/0.15.1
      '@microsoft/tsdoc-config': mirrors.tencent.com/@microsoft/tsdoc-config/0.17.1
      '@rushstack/node-core-library': mirrors.tencent.com/@rushstack/node-core-library/5.13.1_@types+node@20.19.0
    transitivePeerDependencies:
      - '@types/node'
    dev: true

  mirrors.tencent.com/@microsoft/api-extractor/7.52.8_@types+node@20.19.0:
    resolution: {integrity: sha512-cszYIcjiNscDoMB1CIKZ3My61+JOhpERGlGr54i6bocvGLrcL/wo9o+RNXMBrb7XgLtKaizZWUpqRduQuHQLdg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@microsoft/api-extractor/-/api-extractor-7.52.8.tgz}
    id: mirrors.tencent.com/@microsoft/api-extractor/7.52.8
    name: '@microsoft/api-extractor'
    version: 7.52.8
    hasBin: true
    dependencies:
      '@microsoft/api-extractor-model': mirrors.tencent.com/@microsoft/api-extractor-model/7.30.6_@types+node@20.19.0
      '@microsoft/tsdoc': mirrors.tencent.com/@microsoft/tsdoc/0.15.1
      '@microsoft/tsdoc-config': mirrors.tencent.com/@microsoft/tsdoc-config/0.17.1
      '@rushstack/node-core-library': mirrors.tencent.com/@rushstack/node-core-library/5.13.1_@types+node@20.19.0
      '@rushstack/rig-package': mirrors.tencent.com/@rushstack/rig-package/0.5.3
      '@rushstack/terminal': mirrors.tencent.com/@rushstack/terminal/0.15.3_@types+node@20.19.0
      '@rushstack/ts-command-line': mirrors.tencent.com/@rushstack/ts-command-line/5.0.1_@types+node@20.19.0
      lodash: mirrors.tencent.com/lodash/4.17.21
      minimatch: mirrors.tencent.com/minimatch/3.0.8
      resolve: mirrors.tencent.com/resolve/1.22.10
      semver: mirrors.tencent.com/semver/7.5.4
      source-map: mirrors.tencent.com/source-map/0.6.1
      typescript: mirrors.tencent.com/typescript/5.8.2
    transitivePeerDependencies:
      - '@types/node'
    dev: true

  mirrors.tencent.com/@microsoft/tsdoc-config/0.17.1:
    resolution: {integrity: sha512-UtjIFe0C6oYgTnad4q1QP4qXwLhe6tIpNTRStJ2RZEPIkqQPREAwE5spzVxsdn9UaEMUqhh0AqSx3X4nWAKXWw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@microsoft/tsdoc-config/-/tsdoc-config-0.17.1.tgz}
    name: '@microsoft/tsdoc-config'
    version: 0.17.1
    dependencies:
      '@microsoft/tsdoc': mirrors.tencent.com/@microsoft/tsdoc/0.15.1
      ajv: mirrors.tencent.com/ajv/8.12.0
      jju: mirrors.tencent.com/jju/1.4.0
      resolve: mirrors.tencent.com/resolve/1.22.10
    dev: true

  mirrors.tencent.com/@microsoft/tsdoc/0.15.1:
    resolution: {integrity: sha512-4aErSrCR/On/e5G2hDP0wjooqDdauzEbIq8hIkIe5pXV0rtWJZvdCEKL0ykZxex+IxIwBp0eGeV48hQN07dXtw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz}
    name: '@microsoft/tsdoc'
    version: 0.15.1
    dev: true

  mirrors.tencent.com/@nodelib/fs.scandir/2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': mirrors.tencent.com/@nodelib/fs.stat/2.0.5
      run-parallel: mirrors.tencent.com/run-parallel/1.2.0
    dev: true

  mirrors.tencent.com/@nodelib/fs.stat/2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  mirrors.tencent.com/@nodelib/fs.walk/1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': mirrors.tencent.com/@nodelib/fs.scandir/2.1.5
      fastq: mirrors.tencent.com/fastq/1.19.1
    dev: true

  mirrors.tencent.com/@pkgjs/parseargs/0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@pkgjs/parseargs/-/parseargs-0.11.0.tgz}
    name: '@pkgjs/parseargs'
    version: 0.11.0
    engines: {node: '>=14'}
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@popperjs/core/2.11.8:
    resolution: {integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@popperjs/core/-/core-2.11.8.tgz}
    name: '@popperjs/core'
    version: 2.11.8
    dev: true

  mirrors.tencent.com/@rolldown/pluginutils/1.0.0-beta.11:
    resolution: {integrity: sha512-L/gAA/hyCSuzTF1ftlzUSI/IKr2POHsv1Dd78GfqkR83KMNuswWD61JxGV2L7nRwBBBSDr6R1gCkdTmoN7W4ag==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11.tgz}
    name: '@rolldown/pluginutils'
    version: 1.0.0-beta.11
    dev: true

  mirrors.tencent.com/@rollup/pluginutils/5.1.4:
    resolution: {integrity: sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/pluginutils/-/pluginutils-5.1.4.tgz}
    name: '@rollup/pluginutils'
    version: 5.1.4
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true
    dependencies:
      '@types/estree': mirrors.tencent.com/@types/estree/1.0.8
      estree-walker: mirrors.tencent.com/estree-walker/2.0.2
      picomatch: mirrors.tencent.com/picomatch/4.0.2
    dev: true

  mirrors.tencent.com/@rollup/rollup-android-arm-eabi/4.42.0:
    resolution: {integrity: sha512-gldmAyS9hpj+H6LpRNlcjQWbuKUtb94lodB9uCz71Jm+7BxK1VIOo7y62tZZwxhA7j1ylv/yQz080L5WkS+LoQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.42.0.tgz}
    name: '@rollup/rollup-android-arm-eabi'
    version: 4.42.0
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-android-arm64/4.42.0:
    resolution: {integrity: sha512-bpRipfTgmGFdCZDFLRvIkSNO1/3RGS74aWkJJTFJBH7h3MRV4UijkaEUeOMbi9wxtxYmtAbVcnMtHTPBhLEkaw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.42.0.tgz}
    name: '@rollup/rollup-android-arm64'
    version: 4.42.0
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-darwin-arm64/4.42.0:
    resolution: {integrity: sha512-JxHtA081izPBVCHLKnl6GEA0w3920mlJPLh89NojpU2GsBSB6ypu4erFg/Wx1qbpUbepn0jY4dVWMGZM8gplgA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.42.0.tgz}
    name: '@rollup/rollup-darwin-arm64'
    version: 4.42.0
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-darwin-x64/4.42.0:
    resolution: {integrity: sha512-rv5UZaWVIJTDMyQ3dCEK+m0SAn6G7H3PRc2AZmExvbDvtaDc+qXkei0knQWcI3+c9tEs7iL/4I4pTQoPbNL2SA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.42.0.tgz}
    name: '@rollup/rollup-darwin-x64'
    version: 4.42.0
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-freebsd-arm64/4.42.0:
    resolution: {integrity: sha512-fJcN4uSGPWdpVmvLuMtALUFwCHgb2XiQjuECkHT3lWLZhSQ3MBQ9pq+WoWeJq2PrNxr9rPM1Qx+IjyGj8/c6zQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.42.0.tgz}
    name: '@rollup/rollup-freebsd-arm64'
    version: 4.42.0
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-freebsd-x64/4.42.0:
    resolution: {integrity: sha512-CziHfyzpp8hJpCVE/ZdTizw58gr+m7Y2Xq5VOuCSrZR++th2xWAz4Nqk52MoIIrV3JHtVBhbBsJcAxs6NammOQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.42.0.tgz}
    name: '@rollup/rollup-freebsd-x64'
    version: 4.42.0
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-arm-gnueabihf/4.42.0:
    resolution: {integrity: sha512-UsQD5fyLWm2Fe5CDM7VPYAo+UC7+2Px4Y+N3AcPh/LdZu23YcuGPegQly++XEVaC8XUTFVPscl5y5Cl1twEI4A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.42.0.tgz}
    name: '@rollup/rollup-linux-arm-gnueabihf'
    version: 4.42.0
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-arm-musleabihf/4.42.0:
    resolution: {integrity: sha512-/i8NIrlgc/+4n1lnoWl1zgH7Uo0XK5xK3EDqVTf38KvyYgCU/Rm04+o1VvvzJZnVS5/cWSd07owkzcVasgfIkQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.42.0.tgz}
    name: '@rollup/rollup-linux-arm-musleabihf'
    version: 4.42.0
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-arm64-gnu/4.42.0:
    resolution: {integrity: sha512-eoujJFOvoIBjZEi9hJnXAbWg+Vo1Ov8n/0IKZZcPZ7JhBzxh2A+2NFyeMZIRkY9iwBvSjloKgcvnjTbGKHE44Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-arm64-gnu'
    version: 4.42.0
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-arm64-musl/4.42.0:
    resolution: {integrity: sha512-/3NrcOWFSR7RQUQIuZQChLND36aTU9IYE4j+TB40VU78S+RA0IiqHR30oSh6P1S9f9/wVOenHQnacs/Byb824g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.42.0.tgz}
    name: '@rollup/rollup-linux-arm64-musl'
    version: 4.42.0
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-loongarch64-gnu/4.42.0:
    resolution: {integrity: sha512-O8AplvIeavK5ABmZlKBq9/STdZlnQo7Sle0LLhVA7QT+CiGpNVe197/t8Aph9bhJqbDVGCHpY2i7QyfEDDStDg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-loongarch64-gnu'
    version: 4.42.0
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-powerpc64le-gnu/4.42.0:
    resolution: {integrity: sha512-6Qb66tbKVN7VyQrekhEzbHRxXXFFD8QKiFAwX5v9Xt6FiJ3BnCVBuyBxa2fkFGqxOCSGGYNejxd8ht+q5SnmtA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-powerpc64le-gnu'
    version: 4.42.0
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-riscv64-gnu/4.42.0:
    resolution: {integrity: sha512-KQETDSEBamQFvg/d8jajtRwLNBlGc3aKpaGiP/LvEbnmVUKlFta1vqJqTrvPtsYsfbE/DLg5CC9zyXRX3fnBiA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-riscv64-gnu'
    version: 4.42.0
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-riscv64-musl/4.42.0:
    resolution: {integrity: sha512-qMvnyjcU37sCo/tuC+JqeDKSuukGAd+pVlRl/oyDbkvPJ3awk6G6ua7tyum02O3lI+fio+eM5wsVd66X0jQtxw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.42.0.tgz}
    name: '@rollup/rollup-linux-riscv64-musl'
    version: 4.42.0
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-s390x-gnu/4.42.0:
    resolution: {integrity: sha512-I2Y1ZUgTgU2RLddUHXTIgyrdOwljjkmcZ/VilvaEumtS3Fkuhbw4p4hgHc39Ypwvo2o7sBFNl2MquNvGCa55Iw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-s390x-gnu'
    version: 4.42.0
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-x64-gnu/4.42.0:
    resolution: {integrity: sha512-Gfm6cV6mj3hCUY8TqWa63DB8Mx3NADoFwiJrMpoZ1uESbK8FQV3LXkhfry+8bOniq9pqY1OdsjFWNsSbfjPugw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.42.0.tgz}
    name: '@rollup/rollup-linux-x64-gnu'
    version: 4.42.0
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-linux-x64-musl/4.42.0:
    resolution: {integrity: sha512-g86PF8YZ9GRqkdi0VoGlcDUb4rYtQKyTD1IVtxxN4Hpe7YqLBShA7oHMKU6oKTCi3uxwW4VkIGnOaH/El8de3w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.42.0.tgz}
    name: '@rollup/rollup-linux-x64-musl'
    version: 4.42.0
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-win32-arm64-msvc/4.42.0:
    resolution: {integrity: sha512-+axkdyDGSp6hjyzQ5m1pgcvQScfHnMCcsXkx8pTgy/6qBmWVhtRVlgxjWwDp67wEXXUr0x+vD6tp5W4x6V7u1A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.42.0.tgz}
    name: '@rollup/rollup-win32-arm64-msvc'
    version: 4.42.0
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-win32-ia32-msvc/4.42.0:
    resolution: {integrity: sha512-F+5J9pelstXKwRSDq92J0TEBXn2nfUrQGg+HK1+Tk7VOL09e0gBqUHugZv7SW4MGrYj41oNCUe3IKCDGVlis2g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.42.0.tgz}
    name: '@rollup/rollup-win32-ia32-msvc'
    version: 4.42.0
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rollup/rollup-win32-x64-msvc/4.42.0:
    resolution: {integrity: sha512-LpHiJRwkaVz/LqjHjK8LCi8osq7elmpwujwbXKNW88bM8eeGxavJIKKjkjpMHAh/2xfnrt1ZSnhTv41WYUHYmA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.42.0.tgz}
    name: '@rollup/rollup-win32-x64-msvc'
    version: 4.42.0
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@rushstack/node-core-library/5.13.1_@types+node@20.19.0:
    resolution: {integrity: sha512-5yXhzPFGEkVc9Fu92wsNJ9jlvdwz4RNb2bMso+/+TH0nMm1jDDDsOIf4l8GAkPxGuwPw5DH24RliWVfSPhlW/Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rushstack/node-core-library/-/node-core-library-5.13.1.tgz}
    id: mirrors.tencent.com/@rushstack/node-core-library/5.13.1
    name: '@rushstack/node-core-library'
    version: 5.13.1
    peerDependencies:
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      ajv: mirrors.tencent.com/ajv/8.13.0
      ajv-draft-04: mirrors.tencent.com/ajv-draft-04/1.0.0_ajv@8.13.0
      ajv-formats: mirrors.tencent.com/ajv-formats/3.0.1
      fs-extra: mirrors.tencent.com/fs-extra/11.3.0
      import-lazy: mirrors.tencent.com/import-lazy/4.0.0
      jju: mirrors.tencent.com/jju/1.4.0
      resolve: mirrors.tencent.com/resolve/1.22.10
      semver: mirrors.tencent.com/semver/7.5.4
    dev: true

  mirrors.tencent.com/@rushstack/rig-package/0.5.3:
    resolution: {integrity: sha512-olzSSjYrvCNxUFZowevC3uz8gvKr3WTpHQ7BkpjtRpA3wK+T0ybep/SRUMfr195gBzJm5gaXw0ZMgjIyHqJUow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rushstack/rig-package/-/rig-package-0.5.3.tgz}
    name: '@rushstack/rig-package'
    version: 0.5.3
    dependencies:
      resolve: mirrors.tencent.com/resolve/1.22.10
      strip-json-comments: mirrors.tencent.com/strip-json-comments/3.1.1
    dev: true

  mirrors.tencent.com/@rushstack/terminal/0.15.3_@types+node@20.19.0:
    resolution: {integrity: sha512-DGJ0B2Vm69468kZCJkPj3AH5nN+nR9SPmC0rFHtzsS4lBQ7/dgOwtwVxYP7W9JPDMuRBkJ4KHmWKr036eJsj9g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rushstack/terminal/-/terminal-0.15.3.tgz}
    id: mirrors.tencent.com/@rushstack/terminal/0.15.3
    name: '@rushstack/terminal'
    version: 0.15.3
    peerDependencies:
      '@types/node': '*'
    peerDependenciesMeta:
      '@types/node':
        optional: true
    dependencies:
      '@rushstack/node-core-library': mirrors.tencent.com/@rushstack/node-core-library/5.13.1_@types+node@20.19.0
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      supports-color: mirrors.tencent.com/supports-color/8.1.1
    dev: true

  mirrors.tencent.com/@rushstack/ts-command-line/5.0.1_@types+node@20.19.0:
    resolution: {integrity: sha512-bsbUucn41UXrQK7wgM8CNM/jagBytEyJqXw/umtI8d68vFm1Jwxh1OtLrlW7uGZgjCWiiPH6ooUNa1aVsuVr3Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@rushstack/ts-command-line/-/ts-command-line-5.0.1.tgz}
    id: mirrors.tencent.com/@rushstack/ts-command-line/5.0.1
    name: '@rushstack/ts-command-line'
    version: 5.0.1
    dependencies:
      '@rushstack/terminal': mirrors.tencent.com/@rushstack/terminal/0.15.3_@types+node@20.19.0
      '@types/argparse': mirrors.tencent.com/@types/argparse/1.0.38
      argparse: mirrors.tencent.com/argparse/1.0.10
      string-argv: mirrors.tencent.com/string-argv/0.3.2
    transitivePeerDependencies:
      - '@types/node'
    dev: true

  mirrors.tencent.com/@sindresorhus/is/4.6.0:
    resolution: {integrity: sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@sindresorhus/is/-/is-4.6.0.tgz}
    name: '@sindresorhus/is'
    version: 4.6.0
    engines: {node: '>=10'}
    dev: false

  mirrors.tencent.com/@swc/core-darwin-arm64/1.11.31:
    resolution: {integrity: sha512-NTEaYOts0OGSbJZc0O74xsji+64JrF1stmBii6D5EevWEtrY4wlZhm8SiP/qPrOB+HqtAihxWIukWkP2aSdGSQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-darwin-arm64/-/core-darwin-arm64-1.11.31.tgz}
    name: '@swc/core-darwin-arm64'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-darwin-x64/1.11.31:
    resolution: {integrity: sha512-THSGaSwT96JwXDwuXQ6yFBbn+xDMdyw7OmBpnweAWsh5DhZmQkALEm1DgdQO3+rrE99MkmzwAfclc0UmYro/OA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-darwin-x64/-/core-darwin-x64-1.11.31.tgz}
    name: '@swc/core-darwin-x64'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-linux-arm-gnueabihf/1.11.31:
    resolution: {integrity: sha512-laKtQFnW7KHgE57Hx32os2SNAogcuIDxYE+3DYIOmDMqD7/1DCfJe6Rln2N9WcOw6HuDbDpyQavIwZNfSAa8vQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.11.31.tgz}
    name: '@swc/core-linux-arm-gnueabihf'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-linux-arm64-gnu/1.11.31:
    resolution: {integrity: sha512-T+vGw9aPE1YVyRxRr1n7NAdkbgzBzrXCCJ95xAZc/0+WUwmL77Z+js0J5v1KKTRxw4FvrslNCOXzMWrSLdwPSA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.11.31.tgz}
    name: '@swc/core-linux-arm64-gnu'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-linux-arm64-musl/1.11.31:
    resolution: {integrity: sha512-Mztp5NZkyd5MrOAG+kl+QSn0lL4Uawd4CK4J7wm97Hs44N9DHGIG5nOz7Qve1KZo407Y25lTxi/PqzPKHo61zQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.11.31.tgz}
    name: '@swc/core-linux-arm64-musl'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-linux-x64-gnu/1.11.31:
    resolution: {integrity: sha512-DDVE0LZcXOWwOqFU1Xi7gdtiUg3FHA0vbGb3trjWCuI1ZtDZHEQYL4M3/2FjqKZtIwASrDvO96w91okZbXhvMg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.11.31.tgz}
    name: '@swc/core-linux-x64-gnu'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-linux-x64-musl/1.11.31:
    resolution: {integrity: sha512-mJA1MzPPRIfaBUHZi0xJQ4vwL09MNWDeFtxXb0r4Yzpf0v5Lue9ymumcBPmw/h6TKWms+Non4+TDquAsweuKSw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.11.31.tgz}
    name: '@swc/core-linux-x64-musl'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-win32-arm64-msvc/1.11.31:
    resolution: {integrity: sha512-RdtakUkNVAb/FFIMw3LnfNdlH1/ep6KgiPDRlmyUfd0WdIQ3OACmeBegEFNFTzi7gEuzy2Yxg4LWf4IUVk8/bg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.11.31.tgz}
    name: '@swc/core-win32-arm64-msvc'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-win32-ia32-msvc/1.11.31:
    resolution: {integrity: sha512-hErXdCGsg7swWdG1fossuL8542I59xV+all751mYlBoZ8kOghLSKObGQTkBbuNvc0sUKWfWg1X0iBuIhAYar+w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.11.31.tgz}
    name: '@swc/core-win32-ia32-msvc'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core-win32-x64-msvc/1.11.31:
    resolution: {integrity: sha512-5t7SGjUBMMhF9b5j17ml/f/498kiBJNf4vZFNM421UGUEETdtjPN9jZIuQrowBkoFGJTCVL/ECM4YRtTH30u/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.11.31.tgz}
    name: '@swc/core-win32-x64-msvc'
    version: 1.11.31
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/@swc/core/1.11.31:
    resolution: {integrity: sha512-mAby9aUnKRjMEA7v8cVZS9Ah4duoRBnX7X6r5qrhTxErx+68MoY1TPrVwj/66/SWN3Bl+jijqAqoB8Qx0QE34A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/core/-/core-1.11.31.tgz}
    name: '@swc/core'
    version: 1.11.31
    engines: {node: '>=10'}
    requiresBuild: true
    peerDependencies:
      '@swc/helpers': '>=0.5.17'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@swc/counter': mirrors.tencent.com/@swc/counter/0.1.3
      '@swc/types': mirrors.tencent.com/@swc/types/0.1.22
    optionalDependencies:
      '@swc/core-darwin-arm64': mirrors.tencent.com/@swc/core-darwin-arm64/1.11.31
      '@swc/core-darwin-x64': mirrors.tencent.com/@swc/core-darwin-x64/1.11.31
      '@swc/core-linux-arm-gnueabihf': mirrors.tencent.com/@swc/core-linux-arm-gnueabihf/1.11.31
      '@swc/core-linux-arm64-gnu': mirrors.tencent.com/@swc/core-linux-arm64-gnu/1.11.31
      '@swc/core-linux-arm64-musl': mirrors.tencent.com/@swc/core-linux-arm64-musl/1.11.31
      '@swc/core-linux-x64-gnu': mirrors.tencent.com/@swc/core-linux-x64-gnu/1.11.31
      '@swc/core-linux-x64-musl': mirrors.tencent.com/@swc/core-linux-x64-musl/1.11.31
      '@swc/core-win32-arm64-msvc': mirrors.tencent.com/@swc/core-win32-arm64-msvc/1.11.31
      '@swc/core-win32-ia32-msvc': mirrors.tencent.com/@swc/core-win32-ia32-msvc/1.11.31
      '@swc/core-win32-x64-msvc': mirrors.tencent.com/@swc/core-win32-x64-msvc/1.11.31
    dev: true

  mirrors.tencent.com/@swc/counter/0.1.3:
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/counter/-/counter-0.1.3.tgz}
    name: '@swc/counter'
    version: 0.1.3
    dev: true

  mirrors.tencent.com/@swc/types/0.1.22:
    resolution: {integrity: sha512-D13mY/ZA4PPEFSy6acki9eBT/3WgjMoRqNcdpIvjaYLQ44Xk5BdaL7UkDxAh6Z9UOe7tCCp67BVmZCojYp9owg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@swc/types/-/types-0.1.22.tgz}
    name: '@swc/types'
    version: 0.1.22
    dependencies:
      '@swc/counter': mirrors.tencent.com/@swc/counter/0.1.3
    dev: true

  mirrors.tencent.com/@szmarczak/http-timer/4.0.6:
    resolution: {integrity: sha512-4BAffykYOgO+5nzBWYwE3W90sBgLJoUPRWWcL8wlyiM8IB8ipJz3UMJ9KXQd1RKQXpKp8Tutn80HZtWsu2u76w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@szmarczak/http-timer/-/http-timer-4.0.6.tgz}
    name: '@szmarczak/http-timer'
    version: 4.0.6
    engines: {node: '>=10'}
    dependencies:
      defer-to-connect: mirrors.tencent.com/defer-to-connect/2.0.1
    dev: false

  mirrors.tencent.com/@tootallnate/once/2.0.0:
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@tootallnate/once/-/once-2.0.0.tgz}
    name: '@tootallnate/once'
    version: 2.0.0
    engines: {node: '>= 10'}
    dev: true

  mirrors.tencent.com/@types/argparse/1.0.38:
    resolution: {integrity: sha512-ebDJ9b0e702Yr7pWgB0jzm+CX4Srzz8RcXtLJDJB+BSccqMa36uyH/zUsSYao5+BD1ytv3k3rPYCq4mAE1hsXA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/argparse/-/argparse-1.0.38.tgz}
    name: '@types/argparse'
    version: 1.0.38
    dev: true

  mirrors.tencent.com/@types/cacheable-request/6.0.3:
    resolution: {integrity: sha512-IQ3EbTzGxIigb1I3qPZc1rWJnH0BmSKv5QYTalEwweFvyBDLSAe24zP0le/hyi7ecGfZVlIVAg4BZqb8WBwKqw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/cacheable-request/-/cacheable-request-6.0.3.tgz}
    name: '@types/cacheable-request'
    version: 6.0.3
    dependencies:
      '@types/http-cache-semantics': mirrors.tencent.com/@types/http-cache-semantics/4.0.4
      '@types/keyv': mirrors.tencent.com/@types/keyv/3.1.4
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      '@types/responselike': mirrors.tencent.com/@types/responselike/1.0.3
    dev: false

  mirrors.tencent.com/@types/debug/4.1.12:
    resolution: {integrity: sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/debug/-/debug-4.1.12.tgz}
    name: '@types/debug'
    version: 4.1.12
    dependencies:
      '@types/ms': mirrors.tencent.com/@types/ms/2.1.0
    dev: true

  mirrors.tencent.com/@types/estree/1.0.7:
    resolution: {integrity: sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/estree/-/estree-1.0.7.tgz}
    name: '@types/estree'
    version: 1.0.7
    dev: true

  mirrors.tencent.com/@types/estree/1.0.8:
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/estree/-/estree-1.0.8.tgz}
    name: '@types/estree'
    version: 1.0.8
    dev: true

  mirrors.tencent.com/@types/fs-extra/9.0.13:
    resolution: {integrity: sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/fs-extra/-/fs-extra-9.0.13.tgz}
    name: '@types/fs-extra'
    version: 9.0.13
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
    dev: true

  mirrors.tencent.com/@types/http-cache-semantics/4.0.4:
    resolution: {integrity: sha512-1m0bIFVc7eJWyve9S0RnuRgcQqF/Xd5QsUZAZeQFr1Q3/p9JWoQQEqmVy+DPTNpGXwhgIetAoYF8JSc33q29QA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz}
    name: '@types/http-cache-semantics'
    version: 4.0.4
    dev: false

  mirrors.tencent.com/@types/json-schema/7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/json-schema/-/json-schema-7.0.15.tgz}
    name: '@types/json-schema'
    version: 7.0.15
    dev: true

  mirrors.tencent.com/@types/keyv/3.1.4:
    resolution: {integrity: sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/keyv/-/keyv-3.1.4.tgz}
    name: '@types/keyv'
    version: 3.1.4
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
    dev: false

  mirrors.tencent.com/@types/ms/2.1.0:
    resolution: {integrity: sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/ms/-/ms-2.1.0.tgz}
    name: '@types/ms'
    version: 2.1.0
    dev: true

  mirrors.tencent.com/@types/node/20.19.0:
    resolution: {integrity: sha512-hfrc+1tud1xcdVTABC2JiomZJEklMcXYNTVtZLAeqTVWD+qL5jkHKT+1lOtqDdGxt+mB53DTtiz673vfjU8D1Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/node/-/node-20.19.0.tgz}
    name: '@types/node'
    version: 20.19.0
    dependencies:
      undici-types: mirrors.tencent.com/undici-types/6.21.0

  mirrors.tencent.com/@types/parse-json/4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/parse-json/-/parse-json-4.0.2.tgz}
    name: '@types/parse-json'
    version: 4.0.2
    dev: true

  mirrors.tencent.com/@types/plist/3.0.5:
    resolution: {integrity: sha512-E6OCaRmAe4WDmWNsL/9RMqdkkzDCY1etutkflWk4c+AcjDU07Pcz1fQwTX0TQz+Pxqn9i4L1TU3UFpjnrcDgxA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/plist/-/plist-3.0.5.tgz}
    name: '@types/plist'
    version: 3.0.5
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      xmlbuilder: mirrors.tencent.com/xmlbuilder/15.1.1
    dev: true
    optional: true

  mirrors.tencent.com/@types/prop-types/15.7.15:
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/prop-types/-/prop-types-15.7.15.tgz}
    name: '@types/prop-types'
    version: 15.7.15
    dev: true

  mirrors.tencent.com/@types/react-dom/18.3.7_@types+react@18.3.23:
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/react-dom/-/react-dom-18.3.7.tgz}
    id: mirrors.tencent.com/@types/react-dom/18.3.7
    name: '@types/react-dom'
    version: 18.3.7
    peerDependencies:
      '@types/react': ^18.0.0
    dependencies:
      '@types/react': mirrors.tencent.com/@types/react/18.3.23
    dev: true

  mirrors.tencent.com/@types/react/18.3.23:
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/react/-/react-18.3.23.tgz}
    name: '@types/react'
    version: 18.3.23
    dependencies:
      '@types/prop-types': mirrors.tencent.com/@types/prop-types/15.7.15
      csstype: mirrors.tencent.com/csstype/3.1.3
    dev: true

  mirrors.tencent.com/@types/responselike/1.0.3:
    resolution: {integrity: sha512-H/+L+UkTV33uf49PH5pCAUBVPNj2nDBXTN+qS1dOwyyg24l3CcicicCA7ca+HMvJBZcFgl5r8e+RR6elsb4Lyw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/responselike/-/responselike-1.0.3.tgz}
    name: '@types/responselike'
    version: 1.0.3
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
    dev: false

  mirrors.tencent.com/@types/semver/7.7.0:
    resolution: {integrity: sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/semver/-/semver-7.7.0.tgz}
    name: '@types/semver'
    version: 7.7.0
    dev: true

  mirrors.tencent.com/@types/sortablejs/1.15.8:
    resolution: {integrity: sha512-b79830lW+RZfwaztgs1aVPgbasJ8e7AXtZYHTELNXZPsERt4ymJdjV4OccDbHQAvHrCcFpbF78jkm0R6h/pZVg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/sortablejs/-/sortablejs-1.15.8.tgz}
    name: '@types/sortablejs'
    version: 1.15.8
    dev: true

  mirrors.tencent.com/@types/tinycolor2/1.4.6:
    resolution: {integrity: sha512-iEN8J0BoMnsWBqjVbWH/c0G0Hh7O21lpR2/+PrvAVgWdzL7eexIFm4JN/Wn10PTcmNdtS6U67r499mlWMXOxNw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/tinycolor2/-/tinycolor2-1.4.6.tgz}
    name: '@types/tinycolor2'
    version: 1.4.6
    dev: true

  mirrors.tencent.com/@types/validator/13.15.1:
    resolution: {integrity: sha512-9gG6ogYcoI2mCMLdcO0NYI0AYrbxIjv0MDmy/5Ywo6CpWWrqYayc+mmgxRsCgtcGJm9BSbXkMsmxGah1iGHAAQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/validator/-/validator-13.15.1.tgz}
    name: '@types/validator'
    version: 13.15.1
    dev: true

  mirrors.tencent.com/@types/verror/1.10.11:
    resolution: {integrity: sha512-RlDm9K7+o5stv0Co8i8ZRGxDbrTxhJtgjqjFyVh/tXQyl/rYtTKlnTvZ88oSTeYREWurwx20Js4kTuKCsFkUtg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/verror/-/verror-1.10.11.tgz}
    name: '@types/verror'
    version: 1.10.11
    dev: true
    optional: true

  mirrors.tencent.com/@types/yauzl/2.10.3:
    resolution: {integrity: sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@types/yauzl/-/yauzl-2.10.3.tgz}
    name: '@types/yauzl'
    version: 2.10.3
    requiresBuild: true
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
    dev: false
    optional: true

  mirrors.tencent.com/@typescript-eslint/eslint-plugin/6.21.0_kuceqbxaaku7xpinkil3t6nsce:
    resolution: {integrity: sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/eslint-plugin/-/eslint-plugin-6.21.0.tgz}
    id: mirrors.tencent.com/@typescript-eslint/eslint-plugin/6.21.0
    name: '@typescript-eslint/eslint-plugin'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^6.0.0 || ^6.0.0-alpha
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@eslint-community/regexpp': mirrors.tencent.com/@eslint-community/regexpp/4.12.1
      '@typescript-eslint/parser': mirrors.tencent.com/@typescript-eslint/parser/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu
      '@typescript-eslint/scope-manager': mirrors.tencent.com/@typescript-eslint/scope-manager/6.21.0
      '@typescript-eslint/type-utils': mirrors.tencent.com/@typescript-eslint/type-utils/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu
      '@typescript-eslint/utils': mirrors.tencent.com/@typescript-eslint/utils/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu
      '@typescript-eslint/visitor-keys': mirrors.tencent.com/@typescript-eslint/visitor-keys/6.21.0
      debug: mirrors.tencent.com/debug/4.4.1
      eslint: mirrors.tencent.com/eslint/8.57.1
      graphemer: mirrors.tencent.com/graphemer/1.4.0
      ignore: mirrors.tencent.com/ignore/5.3.2
      natural-compare: mirrors.tencent.com/natural-compare/1.4.0
      semver: mirrors.tencent.com/semver/7.7.2
      ts-api-utils: mirrors.tencent.com/ts-api-utils/1.4.3_typescript@5.8.3
      typescript: mirrors.tencent.com/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@typescript-eslint/parser/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu:
    resolution: {integrity: sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/parser/-/parser-6.21.0.tgz}
    id: mirrors.tencent.com/@typescript-eslint/parser/6.21.0
    name: '@typescript-eslint/parser'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/scope-manager': mirrors.tencent.com/@typescript-eslint/scope-manager/6.21.0
      '@typescript-eslint/types': mirrors.tencent.com/@typescript-eslint/types/6.21.0
      '@typescript-eslint/typescript-estree': mirrors.tencent.com/@typescript-eslint/typescript-estree/6.21.0_typescript@5.8.3
      '@typescript-eslint/visitor-keys': mirrors.tencent.com/@typescript-eslint/visitor-keys/6.21.0
      debug: mirrors.tencent.com/debug/4.4.1
      eslint: mirrors.tencent.com/eslint/8.57.1
      typescript: mirrors.tencent.com/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@typescript-eslint/scope-manager/6.21.0:
    resolution: {integrity: sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/scope-manager/-/scope-manager-6.21.0.tgz}
    name: '@typescript-eslint/scope-manager'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': mirrors.tencent.com/@typescript-eslint/types/6.21.0
      '@typescript-eslint/visitor-keys': mirrors.tencent.com/@typescript-eslint/visitor-keys/6.21.0
    dev: true

  mirrors.tencent.com/@typescript-eslint/type-utils/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu:
    resolution: {integrity: sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/type-utils/-/type-utils-6.21.0.tgz}
    id: mirrors.tencent.com/@typescript-eslint/type-utils/6.21.0
    name: '@typescript-eslint/type-utils'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/typescript-estree': mirrors.tencent.com/@typescript-eslint/typescript-estree/6.21.0_typescript@5.8.3
      '@typescript-eslint/utils': mirrors.tencent.com/@typescript-eslint/utils/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu
      debug: mirrors.tencent.com/debug/4.4.1
      eslint: mirrors.tencent.com/eslint/8.57.1
      ts-api-utils: mirrors.tencent.com/ts-api-utils/1.4.3_typescript@5.8.3
      typescript: mirrors.tencent.com/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@typescript-eslint/types/6.21.0:
    resolution: {integrity: sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/types/-/types-6.21.0.tgz}
    name: '@typescript-eslint/types'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    dev: true

  mirrors.tencent.com/@typescript-eslint/typescript-estree/6.21.0_typescript@5.8.3:
    resolution: {integrity: sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/typescript-estree/-/typescript-estree-6.21.0.tgz}
    id: mirrors.tencent.com/@typescript-eslint/typescript-estree/6.21.0
    name: '@typescript-eslint/typescript-estree'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': mirrors.tencent.com/@typescript-eslint/types/6.21.0
      '@typescript-eslint/visitor-keys': mirrors.tencent.com/@typescript-eslint/visitor-keys/6.21.0
      debug: mirrors.tencent.com/debug/4.4.1
      globby: mirrors.tencent.com/globby/11.1.0
      is-glob: mirrors.tencent.com/is-glob/4.0.3
      minimatch: mirrors.tencent.com/minimatch/9.0.3
      semver: mirrors.tencent.com/semver/7.7.2
      ts-api-utils: mirrors.tencent.com/ts-api-utils/1.4.3_typescript@5.8.3
      typescript: mirrors.tencent.com/typescript/5.8.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/@typescript-eslint/utils/6.21.0_hzt6xcfnpp4qecssyxfdrtmoeu:
    resolution: {integrity: sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/utils/-/utils-6.21.0.tgz}
    id: mirrors.tencent.com/@typescript-eslint/utils/6.21.0
    name: '@typescript-eslint/utils'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
    dependencies:
      '@eslint-community/eslint-utils': mirrors.tencent.com/@eslint-community/eslint-utils/4.7.0_eslint@8.57.1
      '@types/json-schema': mirrors.tencent.com/@types/json-schema/7.0.15
      '@types/semver': mirrors.tencent.com/@types/semver/7.7.0
      '@typescript-eslint/scope-manager': mirrors.tencent.com/@typescript-eslint/scope-manager/6.21.0
      '@typescript-eslint/types': mirrors.tencent.com/@typescript-eslint/types/6.21.0
      '@typescript-eslint/typescript-estree': mirrors.tencent.com/@typescript-eslint/typescript-estree/6.21.0_typescript@5.8.3
      eslint: mirrors.tencent.com/eslint/8.57.1
      semver: mirrors.tencent.com/semver/7.7.2
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  mirrors.tencent.com/@typescript-eslint/visitor-keys/6.21.0:
    resolution: {integrity: sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@typescript-eslint/visitor-keys/-/visitor-keys-6.21.0.tgz}
    name: '@typescript-eslint/visitor-keys'
    version: 6.21.0
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': mirrors.tencent.com/@typescript-eslint/types/6.21.0
      eslint-visitor-keys: mirrors.tencent.com/eslint-visitor-keys/3.4.3
    dev: true

  mirrors.tencent.com/@ungap/structured-clone/1.3.0:
    resolution: {integrity: sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@ungap/structured-clone/-/structured-clone-1.3.0.tgz}
    name: '@ungap/structured-clone'
    version: 1.3.0
    dev: true

  mirrors.tencent.com/@vitejs/plugin-react-swc/3.10.2_vite@5.4.19:
    resolution: {integrity: sha512-xD3Rdvrt5LgANug7WekBn1KhcvLn1H3jNBfJRL3reeOIua/WnZOEV5qi5qIBq5T8R0jUDmRtxuvk4bPhzGHDWw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vitejs/plugin-react-swc/-/plugin-react-swc-3.10.2.tgz}
    id: mirrors.tencent.com/@vitejs/plugin-react-swc/3.10.2
    name: '@vitejs/plugin-react-swc'
    version: 3.10.2
    peerDependencies:
      vite: ^4 || ^5 || ^6 || ^7.0.0-beta.0
    dependencies:
      '@rolldown/pluginutils': mirrors.tencent.com/@rolldown/pluginutils/1.0.0-beta.11
      '@swc/core': mirrors.tencent.com/@swc/core/1.11.31
      vite: mirrors.tencent.com/vite/5.4.19_@types+node@20.19.0
    transitivePeerDependencies:
      - '@swc/helpers'
    dev: true

  mirrors.tencent.com/@volar/language-core/2.4.14:
    resolution: {integrity: sha512-X6beusV0DvuVseaOEy7GoagS4rYHgDHnTrdOj5jeUb49fW5ceQyP9Ej5rBhqgz2wJggl+2fDbbojq1XKaxDi6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@volar/language-core/-/language-core-2.4.14.tgz}
    name: '@volar/language-core'
    version: 2.4.14
    dependencies:
      '@volar/source-map': mirrors.tencent.com/@volar/source-map/2.4.14
    dev: true

  mirrors.tencent.com/@volar/source-map/2.4.14:
    resolution: {integrity: sha512-5TeKKMh7Sfxo8021cJfmBzcjfY1SsXsPMMjMvjY7ivesdnybqqS+GxGAoXHAOUawQTwtdUxgP65Im+dEmvWtYQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@volar/source-map/-/source-map-2.4.14.tgz}
    name: '@volar/source-map'
    version: 2.4.14
    dev: true

  mirrors.tencent.com/@volar/typescript/2.4.14:
    resolution: {integrity: sha512-p8Z6f/bZM3/HyCdRNFZOEEzts51uV8WHeN8Tnfnm2EBv6FDB2TQLzfVx7aJvnl8ofKAOnS64B2O8bImBFaauRw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@volar/typescript/-/typescript-2.4.14.tgz}
    name: '@volar/typescript'
    version: 2.4.14
    dependencies:
      '@volar/language-core': mirrors.tencent.com/@volar/language-core/2.4.14
      path-browserify: mirrors.tencent.com/path-browserify/1.0.1
      vscode-uri: mirrors.tencent.com/vscode-uri/3.1.0
    dev: true

  mirrors.tencent.com/@vue/compiler-core/3.5.16:
    resolution: {integrity: sha512-AOQS2eaQOaaZQoL1u+2rCJIKDruNXVBZSiUD3chnUrsoX5ZTQMaCvXlWNIfxBJuU15r1o7+mpo5223KVtIhAgQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vue/compiler-core/-/compiler-core-3.5.16.tgz}
    name: '@vue/compiler-core'
    version: 3.5.16
    dependencies:
      '@babel/parser': mirrors.tencent.com/@babel/parser/7.27.5
      '@vue/shared': mirrors.tencent.com/@vue/shared/3.5.16
      entities: mirrors.tencent.com/entities/4.5.0
      estree-walker: mirrors.tencent.com/estree-walker/2.0.2
      source-map-js: mirrors.tencent.com/source-map-js/1.2.1
    dev: true

  mirrors.tencent.com/@vue/compiler-dom/3.5.16:
    resolution: {integrity: sha512-SSJIhBr/teipXiXjmWOVWLnxjNGo65Oj/8wTEQz0nqwQeP75jWZ0n4sF24Zxoht1cuJoWopwj0J0exYwCJ0dCQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz}
    name: '@vue/compiler-dom'
    version: 3.5.16
    dependencies:
      '@vue/compiler-core': mirrors.tencent.com/@vue/compiler-core/3.5.16
      '@vue/shared': mirrors.tencent.com/@vue/shared/3.5.16
    dev: true

  mirrors.tencent.com/@vue/compiler-vue2/2.7.16:
    resolution: {integrity: sha512-qYC3Psj9S/mfu9uVi5WvNZIzq+xnXMhOwbTFKKDD7b1lhpnn71jXSFdTQ+WsIEk0ONCd7VV2IMm7ONl6tbQ86A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vue/compiler-vue2/-/compiler-vue2-2.7.16.tgz}
    name: '@vue/compiler-vue2'
    version: 2.7.16
    dependencies:
      de-indent: mirrors.tencent.com/de-indent/1.0.2
      he: mirrors.tencent.com/he/1.2.0
    dev: true

  mirrors.tencent.com/@vue/language-core/2.2.0_typescript@5.8.3:
    resolution: {integrity: sha512-O1ZZFaaBGkKbsRfnVH1ifOK1/1BUkyK+3SQsfnh6PmMmD4qJcTU8godCeA96jjDRTL6zgnK7YzCHfaUlH2r0Mw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vue/language-core/-/language-core-2.2.0.tgz}
    id: mirrors.tencent.com/@vue/language-core/2.2.0
    name: '@vue/language-core'
    version: 2.2.0
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@volar/language-core': mirrors.tencent.com/@volar/language-core/2.4.14
      '@vue/compiler-dom': mirrors.tencent.com/@vue/compiler-dom/3.5.16
      '@vue/compiler-vue2': mirrors.tencent.com/@vue/compiler-vue2/2.7.16
      '@vue/shared': mirrors.tencent.com/@vue/shared/3.5.16
      alien-signals: mirrors.tencent.com/alien-signals/0.4.14
      minimatch: mirrors.tencent.com/minimatch/9.0.5
      muggle-string: mirrors.tencent.com/muggle-string/0.4.1
      path-browserify: mirrors.tencent.com/path-browserify/1.0.1
      typescript: mirrors.tencent.com/typescript/5.8.3
    dev: true

  mirrors.tencent.com/@vue/shared/3.5.16:
    resolution: {integrity: sha512-c/0fWy3Jw6Z8L9FmTyYfkpM5zklnqqa9+a6dz3DvONRKW2NEbh46BP0FHuLFSWi2TnQEtp91Z6zOWNrU6QiyPg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@vue/shared/-/shared-3.5.16.tgz}
    name: '@vue/shared'
    version: 3.5.16
    dev: true

  mirrors.tencent.com/@xmldom/xmldom/0.8.10:
    resolution: {integrity: sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/@xmldom/xmldom/-/xmldom-0.8.10.tgz}
    name: '@xmldom/xmldom'
    version: 0.8.10
    engines: {node: '>=10.0.0'}
    dev: true

  mirrors.tencent.com/acorn-jsx/5.3.2_acorn@8.15.0:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: mirrors.tencent.com/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: mirrors.tencent.com/acorn/8.15.0
    dev: true

  mirrors.tencent.com/acorn/8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/acorn/-/acorn-8.15.0.tgz}
    name: acorn
    version: 8.15.0
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  mirrors.tencent.com/agent-base/6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/agent-base/-/agent-base-6.0.2.tgz}
    name: agent-base
    version: 6.0.2
    engines: {node: '>= 6.0.0'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/ahooks/3.8.5_react@18.3.1:
    resolution: {integrity: sha512-Y+MLoJpBXVdjsnnBjE5rOSPkQ4DK+8i5aPDzLJdIOsCpo/fiAeXcBY1Y7oWgtOK0TpOz0gFa/XcyO1UGdoqLcw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ahooks/-/ahooks-3.8.5.tgz}
    id: mirrors.tencent.com/ahooks/3.8.5
    name: ahooks
    version: 3.8.5
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.27.6
      dayjs: mirrors.tencent.com/dayjs/1.11.13
      intersection-observer: mirrors.tencent.com/intersection-observer/0.12.2
      js-cookie: mirrors.tencent.com/js-cookie/3.0.5
      lodash: mirrors.tencent.com/lodash/4.17.21
      react: mirrors.tencent.com/react/18.3.1
      react-fast-compare: mirrors.tencent.com/react-fast-compare/3.2.2
      resize-observer-polyfill: mirrors.tencent.com/resize-observer-polyfill/1.5.1
      screenfull: mirrors.tencent.com/screenfull/5.2.0
      tslib: mirrors.tencent.com/tslib/2.8.1
    dev: true

  mirrors.tencent.com/ajv-draft-04/1.0.0_ajv@8.13.0:
    resolution: {integrity: sha512-mv00Te6nmYbRp5DCwclxtt7yV/joXJPGS7nM+97GdxvuttCOfgI3K4U25zboyeX0O+myI8ERluxQe5wljMmVIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv-draft-04/-/ajv-draft-04-1.0.0.tgz}
    id: mirrors.tencent.com/ajv-draft-04/1.0.0
    name: ajv-draft-04
    version: 1.0.0
    peerDependencies:
      ajv: ^8.5.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: mirrors.tencent.com/ajv/8.13.0
    dev: true

  mirrors.tencent.com/ajv-formats/1.6.1:
    resolution: {integrity: sha512-4CjkH20If1lhR5CGtqkrVg3bbOtFEG80X9v6jDOIUhbzzbB+UzPBGy8GQhUNVZ0yvMHdMpawCOcy5ydGMsagGQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv-formats/-/ajv-formats-1.6.1.tgz}
    name: ajv-formats
    version: 1.6.1
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: mirrors.tencent.com/ajv/7.2.4
    dev: false

  mirrors.tencent.com/ajv-formats/3.0.1:
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv-formats/-/ajv-formats-3.0.1.tgz}
    name: ajv-formats
    version: 3.0.1
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: mirrors.tencent.com/ajv/8.13.0
    dev: true

  mirrors.tencent.com/ajv-keywords/3.5.2_ajv@6.12.6:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv-keywords/-/ajv-keywords-3.5.2.tgz}
    id: mirrors.tencent.com/ajv-keywords/3.5.2
    name: ajv-keywords
    version: 3.5.2
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: mirrors.tencent.com/ajv/6.12.6
    dev: true

  mirrors.tencent.com/ajv/6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv/-/ajv-6.12.6.tgz}
    name: ajv
    version: 6.12.6
    dependencies:
      fast-deep-equal: mirrors.tencent.com/fast-deep-equal/3.1.3
      fast-json-stable-stringify: mirrors.tencent.com/fast-json-stable-stringify/2.1.0
      json-schema-traverse: mirrors.tencent.com/json-schema-traverse/0.4.1
      uri-js: mirrors.tencent.com/uri-js/4.4.1

  mirrors.tencent.com/ajv/7.2.4:
    resolution: {integrity: sha512-nBeQgg/ZZA3u3SYxyaDvpvDtgZ/EZPF547ARgZBrG9Bhu1vKDwAIjtIf+sDtJUKa2zOcEbmRLBRSyMraS/Oy1A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv/-/ajv-7.2.4.tgz}
    name: ajv
    version: 7.2.4
    dependencies:
      fast-deep-equal: mirrors.tencent.com/fast-deep-equal/3.1.3
      json-schema-traverse: mirrors.tencent.com/json-schema-traverse/1.0.0
      require-from-string: mirrors.tencent.com/require-from-string/2.0.2
      uri-js: mirrors.tencent.com/uri-js/4.4.1
    dev: false

  mirrors.tencent.com/ajv/8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv/-/ajv-8.12.0.tgz}
    name: ajv
    version: 8.12.0
    dependencies:
      fast-deep-equal: mirrors.tencent.com/fast-deep-equal/3.1.3
      json-schema-traverse: mirrors.tencent.com/json-schema-traverse/1.0.0
      require-from-string: mirrors.tencent.com/require-from-string/2.0.2
      uri-js: mirrors.tencent.com/uri-js/4.4.1
    dev: true

  mirrors.tencent.com/ajv/8.13.0:
    resolution: {integrity: sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ajv/-/ajv-8.13.0.tgz}
    name: ajv
    version: 8.13.0
    dependencies:
      fast-deep-equal: mirrors.tencent.com/fast-deep-equal/3.1.3
      json-schema-traverse: mirrors.tencent.com/json-schema-traverse/1.0.0
      require-from-string: mirrors.tencent.com/require-from-string/2.0.2
      uri-js: mirrors.tencent.com/uri-js/4.4.1
    dev: true

  mirrors.tencent.com/alien-signals/0.4.14:
    resolution: {integrity: sha512-itUAVzhczTmP2U5yX67xVpsbbOiquusbWVyA9N+sy6+r6YVbFkahXvNCeEPWEOMhwDYwbVbGHFkVL03N9I5g+Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/alien-signals/-/alien-signals-0.4.14.tgz}
    name: alien-signals
    version: 0.4.14
    dev: true

  mirrors.tencent.com/ansi-regex/5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ansi-regex/-/ansi-regex-5.0.1.tgz}
    name: ansi-regex
    version: 5.0.1
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/ansi-regex/6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ansi-regex/-/ansi-regex-6.1.0.tgz}
    name: ansi-regex
    version: 6.1.0
    engines: {node: '>=12'}
    dev: true

  mirrors.tencent.com/ansi-styles/4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: mirrors.tencent.com/color-convert/2.0.1
    dev: true

  mirrors.tencent.com/ansi-styles/6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ansi-styles/-/ansi-styles-6.2.1.tgz}
    name: ansi-styles
    version: 6.2.1
    engines: {node: '>=12'}
    dev: true

  mirrors.tencent.com/app-builder-bin/4.0.0:
    resolution: {integrity: sha512-xwdG0FJPQMe0M0UA4Tz0zEB8rBJTRA5a476ZawAqiBkMv16GRK5xpXThOjMaEOFnZ6zabejjG4J3da0SXG63KA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/app-builder-bin/-/app-builder-bin-4.0.0.tgz}
    name: app-builder-bin
    version: 4.0.0
    dev: true

  mirrors.tencent.com/app-builder-lib/24.13.3_dmg-builder@24.13.3:
    resolution: {integrity: sha512-FAzX6IBit2POXYGnTCT8YHFO/lr5AapAII6zzhQO3Rw4cEDOgK+t1xhLc5tNcKlicTHlo9zxIwnYCX9X2DLkig==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/app-builder-lib/-/app-builder-lib-24.13.3.tgz}
    id: mirrors.tencent.com/app-builder-lib/24.13.3
    name: app-builder-lib
    version: 24.13.3
    engines: {node: '>=14.0.0'}
    peerDependencies:
      dmg-builder: 24.13.3
      electron-builder-squirrel-windows: 24.13.3
    dependencies:
      '@develar/schema-utils': mirrors.tencent.com/@develar/schema-utils/2.6.5
      '@electron/notarize': mirrors.tencent.com/@electron/notarize/2.2.1
      '@electron/osx-sign': mirrors.tencent.com/@electron/osx-sign/1.0.5
      '@electron/universal': mirrors.tencent.com/@electron/universal/1.5.1
      '@malept/flatpak-bundler': mirrors.tencent.com/@malept/flatpak-bundler/0.4.0
      '@types/fs-extra': mirrors.tencent.com/@types/fs-extra/9.0.13
      async-exit-hook: mirrors.tencent.com/async-exit-hook/2.0.1
      bluebird-lst: mirrors.tencent.com/bluebird-lst/1.0.9
      builder-util: mirrors.tencent.com/builder-util/24.13.1
      builder-util-runtime: mirrors.tencent.com/builder-util-runtime/9.2.4
      chromium-pickle-js: mirrors.tencent.com/chromium-pickle-js/0.2.0
      debug: mirrors.tencent.com/debug/4.4.1
      dmg-builder: mirrors.tencent.com/dmg-builder/24.13.3
      ejs: mirrors.tencent.com/ejs/3.1.10
      electron-publish: mirrors.tencent.com/electron-publish/24.13.1
      form-data: mirrors.tencent.com/form-data/4.0.3
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      hosted-git-info: mirrors.tencent.com/hosted-git-info/4.1.0
      is-ci: mirrors.tencent.com/is-ci/3.0.1
      isbinaryfile: mirrors.tencent.com/isbinaryfile/5.0.4
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
      lazy-val: mirrors.tencent.com/lazy-val/1.0.5
      minimatch: mirrors.tencent.com/minimatch/5.1.6
      read-config-file: mirrors.tencent.com/read-config-file/6.3.2
      sanitize-filename: mirrors.tencent.com/sanitize-filename/1.6.3
      semver: mirrors.tencent.com/semver/7.7.2
      tar: mirrors.tencent.com/tar/6.2.1
      temp-file: mirrors.tencent.com/temp-file/3.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/argparse/1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/argparse/-/argparse-1.0.10.tgz}
    name: argparse
    version: 1.0.10
    dependencies:
      sprintf-js: mirrors.tencent.com/sprintf-js/1.0.3
    dev: true

  mirrors.tencent.com/argparse/2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/argparse/-/argparse-2.0.1.tgz}
    name: argparse
    version: 2.0.1
    dev: true

  mirrors.tencent.com/array-union/2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/array-union/-/array-union-2.1.0.tgz}
    name: array-union
    version: 2.1.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/asn1/0.2.6:
    resolution: {integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/asn1/-/asn1-0.2.6.tgz}
    name: asn1
    version: 0.2.6
    dependencies:
      safer-buffer: mirrors.tencent.com/safer-buffer/2.1.2
    dev: false

  mirrors.tencent.com/assert-plus/1.0.0:
    resolution: {integrity: sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/assert-plus/-/assert-plus-1.0.0.tgz}
    name: assert-plus
    version: 1.0.0
    engines: {node: '>=0.8'}

  mirrors.tencent.com/astral-regex/2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/astral-regex/-/astral-regex-2.0.0.tgz}
    name: astral-regex
    version: 2.0.0
    engines: {node: '>=8'}
    dev: true
    optional: true

  mirrors.tencent.com/async-exit-hook/2.0.1:
    resolution: {integrity: sha512-NW2cX8m1Q7KPA7a5M2ULQeZ2wR5qI5PAbw5L0UOMxdioVk9PMZ0h1TmyZEkPYrCvYjDlFICusOu1dlEKAAeXBw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/async-exit-hook/-/async-exit-hook-2.0.1.tgz}
    name: async-exit-hook
    version: 2.0.1
    engines: {node: '>=0.12.0'}
    dev: true

  mirrors.tencent.com/async/3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/async/-/async-3.2.6.tgz}
    name: async
    version: 3.2.6
    dev: true

  mirrors.tencent.com/asynckit/0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/asynckit/-/asynckit-0.4.0.tgz}
    name: asynckit
    version: 0.4.0

  mirrors.tencent.com/at-least-node/1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/at-least-node/-/at-least-node-1.0.0.tgz}
    name: at-least-node
    version: 1.0.0
    engines: {node: '>= 4.0.0'}
    dev: true

  mirrors.tencent.com/atomically/1.7.0:
    resolution: {integrity: sha512-Xcz9l0z7y9yQ9rdDaxlmaI4uJHf/T8g9hOEzJcsEqX2SjCj4J20uK7+ldkDHMbpJDK76wF7xEIgxc/vSlsfw5w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/atomically/-/atomically-1.7.0.tgz}
    name: atomically
    version: 1.7.0
    engines: {node: '>=10.12.0'}
    dev: false

  mirrors.tencent.com/aws-sign2/0.7.0:
    resolution: {integrity: sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/aws-sign2/-/aws-sign2-0.7.0.tgz}
    name: aws-sign2
    version: 0.7.0
    dev: false

  mirrors.tencent.com/aws4/1.13.2:
    resolution: {integrity: sha512-lHe62zvbTB5eEABUVi/AwVh0ZKY9rMMDhmm+eeyuuUQbQ3+J+fONVQOZyj+DdrvD4BY33uYniyRJ4UJIaSKAfw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/aws4/-/aws4-1.13.2.tgz}
    name: aws4
    version: 1.13.2
    dev: false

  mirrors.tencent.com/axios/1.9.0:
    resolution: {integrity: sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/axios/-/axios-1.9.0.tgz}
    name: axios
    version: 1.9.0
    dependencies:
      follow-redirects: mirrors.tencent.com/follow-redirects/1.15.9
      form-data: mirrors.tencent.com/form-data/4.0.3
      proxy-from-env: mirrors.tencent.com/proxy-from-env/1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  mirrors.tencent.com/babel-plugin-macros/3.1.0:
    resolution: {integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz}
    name: babel-plugin-macros
    version: 3.1.0
    engines: {node: '>=10', npm: '>=6'}
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.27.6
      cosmiconfig: mirrors.tencent.com/cosmiconfig/7.1.0
      resolve: mirrors.tencent.com/resolve/1.22.10
    dev: true

  mirrors.tencent.com/balanced-match/1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2
    dev: true

  mirrors.tencent.com/base64-js/1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/base64-js/-/base64-js-1.5.1.tgz}
    name: base64-js
    version: 1.5.1
    dev: true

  mirrors.tencent.com/bcrypt-pbkdf/1.0.2:
    resolution: {integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz}
    name: bcrypt-pbkdf
    version: 1.0.2
    dependencies:
      tweetnacl: mirrors.tencent.com/tweetnacl/0.14.5
    dev: false

  mirrors.tencent.com/bluebird-lst/1.0.9:
    resolution: {integrity: sha512-7B1Rtx82hjnSD4PGLAjVWeYH3tHAcVUmChh85a3lltKQm6FresXh9ErQo6oAv6CqxttczC3/kEg8SY5NluPuUw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/bluebird-lst/-/bluebird-lst-1.0.9.tgz}
    name: bluebird-lst
    version: 1.0.9
    dependencies:
      bluebird: mirrors.tencent.com/bluebird/3.7.2
    dev: true

  mirrors.tencent.com/bluebird/3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/bluebird/-/bluebird-3.7.2.tgz}
    name: bluebird
    version: 3.7.2
    dev: true

  mirrors.tencent.com/boolean/3.2.0:
    resolution: {integrity: sha512-d0II/GO9uf9lfUHH2BQsjxzRJZBdsjgsBiW4BvhWk/3qoKwQFjIDVN19PfX8F2D/r9PCMTtLWjYVCFrpeYUzsw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/boolean/-/boolean-3.2.0.tgz}
    name: boolean
    version: 3.2.0
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dev: false
    optional: true

  mirrors.tencent.com/brace-expansion/1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-1.1.11.tgz}
    name: brace-expansion
    version: 1.1.11
    dependencies:
      balanced-match: mirrors.tencent.com/balanced-match/1.0.2
      concat-map: mirrors.tencent.com/concat-map/0.0.1
    dev: true

  mirrors.tencent.com/brace-expansion/2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/brace-expansion/-/brace-expansion-2.0.1.tgz}
    name: brace-expansion
    version: 2.0.1
    dependencies:
      balanced-match: mirrors.tencent.com/balanced-match/1.0.2
    dev: true

  mirrors.tencent.com/braces/3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/braces/-/braces-3.0.3.tgz}
    name: braces
    version: 3.0.3
    engines: {node: '>=8'}
    dependencies:
      fill-range: mirrors.tencent.com/fill-range/7.1.1
    dev: true

  mirrors.tencent.com/browser-image-compression/2.0.2:
    resolution: {integrity: sha512-pBLlQyUf6yB8SmmngrcOw3EoS4RpQ1BcylI3T9Yqn7+4nrQTXJD4sJDe5ODnJdrvNMaio5OicFo75rDyJD2Ucw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/browser-image-compression/-/browser-image-compression-2.0.2.tgz}
    name: browser-image-compression
    version: 2.0.2
    dependencies:
      uzip: mirrors.tencent.com/uzip/0.20201231.0
    dev: true

  mirrors.tencent.com/buffer-crc32/0.2.13:
    resolution: {integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/buffer-crc32/-/buffer-crc32-0.2.13.tgz}
    name: buffer-crc32
    version: 0.2.13
    dev: false

  mirrors.tencent.com/buffer-equal/1.0.1:
    resolution: {integrity: sha512-QoV3ptgEaQpvVwbXdSO39iqPQTCxSF7A5U99AxbHYqUdCizL/lH2Z0A2y6nbZucxMEOtNyZfG2s6gsVugGpKkg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/buffer-equal/-/buffer-equal-1.0.1.tgz}
    name: buffer-equal
    version: 1.0.1
    engines: {node: '>=0.4'}
    dev: true

  mirrors.tencent.com/buffer-from/1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/buffer-from/-/buffer-from-1.1.2.tgz}
    name: buffer-from
    version: 1.1.2
    dev: true

  mirrors.tencent.com/buffer/5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/buffer/-/buffer-5.7.1.tgz}
    name: buffer
    version: 5.7.1
    dependencies:
      base64-js: mirrors.tencent.com/base64-js/1.5.1
      ieee754: mirrors.tencent.com/ieee754/1.2.1
    dev: true
    optional: true

  mirrors.tencent.com/builder-util-runtime/9.2.4:
    resolution: {integrity: sha512-upp+biKpN/XZMLim7aguUyW8s0FUpDvOtK6sbanMFDAMBzpHDqdhgVYm6zc9HJ6nWo7u2Lxk60i2M6Jd3aiNrA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/builder-util-runtime/-/builder-util-runtime-9.2.4.tgz}
    name: builder-util-runtime
    version: 9.2.4
    engines: {node: '>=12.0.0'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
      sax: mirrors.tencent.com/sax/1.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/builder-util/24.13.1:
    resolution: {integrity: sha512-NhbCSIntruNDTOVI9fdXz0dihaqX2YuE1D6zZMrwiErzH4ELZHE6mdiB40wEgZNprDia+FghRFgKoAqMZRRjSA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/builder-util/-/builder-util-24.13.1.tgz}
    name: builder-util
    version: 24.13.1
    dependencies:
      '@types/debug': mirrors.tencent.com/@types/debug/4.1.12
      7zip-bin: mirrors.tencent.com/7zip-bin/5.2.0
      app-builder-bin: mirrors.tencent.com/app-builder-bin/4.0.0
      bluebird-lst: mirrors.tencent.com/bluebird-lst/1.0.9
      builder-util-runtime: mirrors.tencent.com/builder-util-runtime/9.2.4
      chalk: mirrors.tencent.com/chalk/4.1.2
      cross-spawn: mirrors.tencent.com/cross-spawn/7.0.6
      debug: mirrors.tencent.com/debug/4.4.1
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      http-proxy-agent: mirrors.tencent.com/http-proxy-agent/5.0.0
      https-proxy-agent: mirrors.tencent.com/https-proxy-agent/5.0.1
      is-ci: mirrors.tencent.com/is-ci/3.0.1
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
      source-map-support: mirrors.tencent.com/source-map-support/0.5.21
      stat-mode: mirrors.tencent.com/stat-mode/1.0.0
      temp-file: mirrors.tencent.com/temp-file/3.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/cacheable-lookup/5.0.4:
    resolution: {integrity: sha512-2/kNscPhpcxrOigMZzbiWF7dz8ilhb/nIHU3EyZiXWXpeq/au8qJ8VhdftMkty3n7Gj6HIGalQG8oiBNB3AJgA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz}
    name: cacheable-lookup
    version: 5.0.4
    engines: {node: '>=10.6.0'}
    dev: false

  mirrors.tencent.com/cacheable-request/7.0.4:
    resolution: {integrity: sha512-v+p6ongsrp0yTGbJXjgxPow2+DL93DASP4kXCDKb8/bwRtt9OEF3whggkkDkGNzgcWy2XaF4a8nZglC7uElscg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cacheable-request/-/cacheable-request-7.0.4.tgz}
    name: cacheable-request
    version: 7.0.4
    engines: {node: '>=8'}
    dependencies:
      clone-response: mirrors.tencent.com/clone-response/1.0.3
      get-stream: mirrors.tencent.com/get-stream/5.2.0
      http-cache-semantics: mirrors.tencent.com/http-cache-semantics/4.2.0
      keyv: mirrors.tencent.com/keyv/4.5.4
      lowercase-keys: mirrors.tencent.com/lowercase-keys/2.0.0
      normalize-url: mirrors.tencent.com/normalize-url/6.1.0
      responselike: mirrors.tencent.com/responselike/2.0.1
    dev: false

  mirrors.tencent.com/call-bind-apply-helpers/1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz}
    name: call-bind-apply-helpers
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: mirrors.tencent.com/es-errors/1.3.0
      function-bind: mirrors.tencent.com/function-bind/1.1.2

  mirrors.tencent.com/callsites/3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/callsites/-/callsites-3.1.0.tgz}
    name: callsites
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  mirrors.tencent.com/caseless/0.12.0:
    resolution: {integrity: sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/caseless/-/caseless-0.12.0.tgz}
    name: caseless
    version: 0.12.0
    dev: false

  mirrors.tencent.com/chalk/4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: mirrors.tencent.com/ansi-styles/4.3.0
      supports-color: mirrors.tencent.com/supports-color/7.2.0
    dev: true

  mirrors.tencent.com/chownr/2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/chownr/-/chownr-2.0.0.tgz}
    name: chownr
    version: 2.0.0
    engines: {node: '>=10'}
    dev: true

  mirrors.tencent.com/chromium-pickle-js/0.2.0:
    resolution: {integrity: sha512-1R5Fho+jBq0DDydt+/vHWj5KJNJCKdARKOCwZUen84I5BreWoLqRLANH1U87eJy1tiASPtMnGqJJq0ZsLoRPOw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/chromium-pickle-js/-/chromium-pickle-js-0.2.0.tgz}
    name: chromium-pickle-js
    version: 0.2.0
    dev: true

  mirrors.tencent.com/ci-info/3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ci-info/-/ci-info-3.9.0.tgz}
    name: ci-info
    version: 3.9.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/classnames/2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/classnames/-/classnames-2.5.1.tgz}
    name: classnames
    version: 2.5.1
    dev: true

  mirrors.tencent.com/cli-truncate/2.1.0:
    resolution: {integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cli-truncate/-/cli-truncate-2.1.0.tgz}
    name: cli-truncate
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      slice-ansi: mirrors.tencent.com/slice-ansi/3.0.0
      string-width: mirrors.tencent.com/string-width/4.2.3
    dev: true
    optional: true

  mirrors.tencent.com/cliui/8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cliui/-/cliui-8.0.1.tgz}
    name: cliui
    version: 8.0.1
    engines: {node: '>=12'}
    dependencies:
      string-width: mirrors.tencent.com/string-width/4.2.3
      strip-ansi: mirrors.tencent.com/strip-ansi/6.0.1
      wrap-ansi: mirrors.tencent.com/wrap-ansi/7.0.0
    dev: true

  mirrors.tencent.com/clone-response/1.0.3:
    resolution: {integrity: sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/clone-response/-/clone-response-1.0.3.tgz}
    name: clone-response
    version: 1.0.3
    dependencies:
      mimic-response: mirrors.tencent.com/mimic-response/1.0.1
    dev: false

  mirrors.tencent.com/color-convert/2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: mirrors.tencent.com/color-name/1.1.4
    dev: true

  mirrors.tencent.com/color-name/1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4
    dev: true

  mirrors.tencent.com/combined-stream/1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/combined-stream/-/combined-stream-1.0.8.tgz}
    name: combined-stream
    version: 1.0.8
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: mirrors.tencent.com/delayed-stream/1.0.0

  mirrors.tencent.com/commander/5.1.0:
    resolution: {integrity: sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/commander/-/commander-5.1.0.tgz}
    name: commander
    version: 5.1.0
    engines: {node: '>= 6'}
    dev: true

  mirrors.tencent.com/compare-version/0.1.2:
    resolution: {integrity: sha512-pJDh5/4wrEnXX/VWRZvruAGHkzKdr46z11OlTPN+VrATlWWhSKewNCJ1futCO5C7eJB3nPMFZA1LeYtcFboZ2A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/compare-version/-/compare-version-0.1.2.tgz}
    name: compare-version
    version: 0.1.2
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/compare-versions/6.1.1:
    resolution: {integrity: sha512-4hm4VPpIecmlg59CHXnRDnqGplJFrbLG4aFEl5vl6cK1u76ws3LLvX7ikFnTDl5vo39sjWD6AaDPYodJp/NNHg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/compare-versions/-/compare-versions-6.1.1.tgz}
    name: compare-versions
    version: 6.1.1
    dev: true

  mirrors.tencent.com/concat-map/0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  mirrors.tencent.com/conf/9.0.2:
    resolution: {integrity: sha512-rLSiilO85qHgaTBIIHQpsv8z+NnVfZq3cKuYNCXN1AOqPzced0GWZEe/A517VldRLyQYXUMyV+vszavE2jSAqw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/conf/-/conf-9.0.2.tgz}
    name: conf
    version: 9.0.2
    engines: {node: '>=10'}
    dependencies:
      ajv: mirrors.tencent.com/ajv/7.2.4
      ajv-formats: mirrors.tencent.com/ajv-formats/1.6.1
      atomically: mirrors.tencent.com/atomically/1.7.0
      debounce-fn: mirrors.tencent.com/debounce-fn/4.0.0
      dot-prop: mirrors.tencent.com/dot-prop/6.0.1
      env-paths: mirrors.tencent.com/env-paths/2.2.1
      json-schema-typed: mirrors.tencent.com/json-schema-typed/7.0.3
      make-dir: mirrors.tencent.com/make-dir/3.1.0
      onetime: mirrors.tencent.com/onetime/5.1.2
      pkg-up: mirrors.tencent.com/pkg-up/3.1.0
      semver: mirrors.tencent.com/semver/7.7.2
    dev: false

  mirrors.tencent.com/confbox/0.1.8:
    resolution: {integrity: sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/confbox/-/confbox-0.1.8.tgz}
    name: confbox
    version: 0.1.8
    dev: true

  mirrors.tencent.com/confbox/0.2.2:
    resolution: {integrity: sha512-1NB+BKqhtNipMsov4xI/NnhCKp9XG9NamYp5PVm9klAT0fsrNPjaFICsCFhNhwZJKNh7zB/3q8qXz0E9oaMNtQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/confbox/-/confbox-0.2.2.tgz}
    name: confbox
    version: 0.2.2
    dev: true

  mirrors.tencent.com/config-file-ts/0.2.6:
    resolution: {integrity: sha512-6boGVaglwblBgJqGyxm4+xCmEGcWgnWHSWHY5jad58awQhB6gftq0G8HbzU39YqCIYHMLAiL1yjwiZ36m/CL8w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/config-file-ts/-/config-file-ts-0.2.6.tgz}
    name: config-file-ts
    version: 0.2.6
    dependencies:
      glob: mirrors.tencent.com/glob/10.4.5
      typescript: mirrors.tencent.com/typescript/5.8.3
    dev: true

  mirrors.tencent.com/convert-source-map/1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/convert-source-map/-/convert-source-map-1.9.0.tgz}
    name: convert-source-map
    version: 1.9.0
    dev: true

  mirrors.tencent.com/core-util-is/1.0.2:
    resolution: {integrity: sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/core-util-is/-/core-util-is-1.0.2.tgz}
    name: core-util-is
    version: 1.0.2

  mirrors.tencent.com/cos-js-sdk-v5/1.10.1:
    resolution: {integrity: sha512-a4SRfCY5g6Z35C7OWe9te/S1zk77rVQzfpvZ33gmTdJQzKxbNbEG7Aw/v453XwVMsQB352FIf7KRMm5Ya/wlZQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cos-js-sdk-v5/-/cos-js-sdk-v5-1.10.1.tgz}
    name: cos-js-sdk-v5
    version: 1.10.1
    requiresBuild: true
    dependencies:
      fast-xml-parser: mirrors.tencent.com/fast-xml-parser/4.5.0
    dev: true

  mirrors.tencent.com/cos-nodejs-sdk-v5/2.15.1:
    resolution: {integrity: sha512-eBfqnuffe1qlIHbCep8eQgXa6Fq44cDjgCv6A2pyB/DAnKxY15DE6vU92Zc55v2t60EGcDapaTQY6DWVauGMRQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cos-nodejs-sdk-v5/-/cos-nodejs-sdk-v5-2.15.1.tgz}
    name: cos-nodejs-sdk-v5
    version: 2.15.1
    engines: {node: '>= 6'}
    dependencies:
      conf: mirrors.tencent.com/conf/9.0.2
      fast-xml-parser: mirrors.tencent.com/fast-xml-parser/4.2.5
      mime-types: mirrors.tencent.com/mime-types/2.1.35
      request: mirrors.tencent.com/request/2.88.2
    dev: false

  mirrors.tencent.com/cosmiconfig/7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cosmiconfig/-/cosmiconfig-7.1.0.tgz}
    name: cosmiconfig
    version: 7.1.0
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': mirrors.tencent.com/@types/parse-json/4.0.2
      import-fresh: mirrors.tencent.com/import-fresh/3.3.1
      parse-json: mirrors.tencent.com/parse-json/5.2.0
      path-type: mirrors.tencent.com/path-type/4.0.0
      yaml: mirrors.tencent.com/yaml/1.10.2
    dev: true

  mirrors.tencent.com/crc/3.8.0:
    resolution: {integrity: sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/crc/-/crc-3.8.0.tgz}
    name: crc
    version: 3.8.0
    dependencies:
      buffer: mirrors.tencent.com/buffer/5.7.1
    dev: true
    optional: true

  mirrors.tencent.com/cross-spawn/7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/cross-spawn/-/cross-spawn-7.0.6.tgz}
    name: cross-spawn
    version: 7.0.6
    engines: {node: '>= 8'}
    dependencies:
      path-key: mirrors.tencent.com/path-key/3.1.1
      shebang-command: mirrors.tencent.com/shebang-command/2.0.0
      which: mirrors.tencent.com/which/2.0.2
    dev: true

  mirrors.tencent.com/csstype/3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/csstype/-/csstype-3.1.3.tgz}
    name: csstype
    version: 3.1.3
    dev: true

  mirrors.tencent.com/dashdash/1.14.1:
    resolution: {integrity: sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dashdash/-/dashdash-1.14.1.tgz}
    name: dashdash
    version: 1.14.1
    engines: {node: '>=0.10'}
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
    dev: false

  mirrors.tencent.com/dayjs/1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dayjs/-/dayjs-1.11.10.tgz}
    name: dayjs
    version: 1.11.10
    dev: true

  mirrors.tencent.com/dayjs/1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dayjs/-/dayjs-1.11.13.tgz}
    name: dayjs
    version: 1.11.13
    dev: true

  mirrors.tencent.com/de-indent/1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/de-indent/-/de-indent-1.0.2.tgz}
    name: de-indent
    version: 1.0.2
    dev: true

  mirrors.tencent.com/debounce-fn/4.0.0:
    resolution: {integrity: sha512-8pYCQiL9Xdcg0UPSD3d+0KMlOjp+KGU5EPwYddgzQ7DATsg4fuUDjQtsYLmWjnk2obnNHgV3vE2Y4jejSOJVBQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/debounce-fn/-/debounce-fn-4.0.0.tgz}
    name: debounce-fn
    version: 4.0.0
    engines: {node: '>=10'}
    dependencies:
      mimic-fn: mirrors.tencent.com/mimic-fn/3.1.0
    dev: false

  mirrors.tencent.com/debug/4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/debug/-/debug-4.4.1.tgz}
    name: debug
    version: 4.4.1
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: mirrors.tencent.com/ms/2.1.3

  mirrors.tencent.com/decompress-response/6.0.0:
    resolution: {integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/decompress-response/-/decompress-response-6.0.0.tgz}
    name: decompress-response
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      mimic-response: mirrors.tencent.com/mimic-response/3.1.0
    dev: false

  mirrors.tencent.com/deep-is/0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: true

  mirrors.tencent.com/defer-to-connect/2.0.1:
    resolution: {integrity: sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/defer-to-connect/-/defer-to-connect-2.0.1.tgz}
    name: defer-to-connect
    version: 2.0.1
    engines: {node: '>=10'}
    dev: false

  mirrors.tencent.com/define-data-property/1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/define-data-property/-/define-data-property-1.1.4.tgz}
    name: define-data-property
    version: 1.1.4
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: mirrors.tencent.com/es-define-property/1.0.1
      es-errors: mirrors.tencent.com/es-errors/1.3.0
      gopd: mirrors.tencent.com/gopd/1.2.0
    dev: false
    optional: true

  mirrors.tencent.com/define-properties/1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/define-properties/-/define-properties-1.2.1.tgz}
    name: define-properties
    version: 1.2.1
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: mirrors.tencent.com/define-data-property/1.1.4
      has-property-descriptors: mirrors.tencent.com/has-property-descriptors/1.0.2
      object-keys: mirrors.tencent.com/object-keys/1.1.1
    dev: false
    optional: true

  mirrors.tencent.com/delayed-stream/1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/delayed-stream/-/delayed-stream-1.0.0.tgz}
    name: delayed-stream
    version: 1.0.0
    engines: {node: '>=0.4.0'}

  mirrors.tencent.com/detect-node/2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/detect-node/-/detect-node-2.1.0.tgz}
    name: detect-node
    version: 2.1.0
    dev: false
    optional: true

  mirrors.tencent.com/dir-compare/3.3.0:
    resolution: {integrity: sha512-J7/et3WlGUCxjdnD3HAAzQ6nsnc0WL6DD7WcwJb7c39iH1+AWfg+9OqzJNaI6PkBwBvm1mhZNL9iY/nRiZXlPg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dir-compare/-/dir-compare-3.3.0.tgz}
    name: dir-compare
    version: 3.3.0
    dependencies:
      buffer-equal: mirrors.tencent.com/buffer-equal/1.0.1
      minimatch: mirrors.tencent.com/minimatch/3.1.2
    dev: true

  mirrors.tencent.com/dir-glob/3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dir-glob/-/dir-glob-3.0.1.tgz}
    name: dir-glob
    version: 3.0.1
    engines: {node: '>=8'}
    dependencies:
      path-type: mirrors.tencent.com/path-type/4.0.0
    dev: true

  mirrors.tencent.com/dmg-builder/24.13.3:
    resolution: {integrity: sha512-rcJUkMfnJpfCboZoOOPf4L29TRtEieHNOeAbYPWPxlaBw/Z1RKrRA86dOI9rwaI4tQSc/RD82zTNHprfUHXsoQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dmg-builder/-/dmg-builder-24.13.3.tgz}
    name: dmg-builder
    version: 24.13.3
    dependencies:
      app-builder-lib: mirrors.tencent.com/app-builder-lib/24.13.3_dmg-builder@24.13.3
      builder-util: mirrors.tencent.com/builder-util/24.13.1
      builder-util-runtime: mirrors.tencent.com/builder-util-runtime/9.2.4
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      iconv-lite: mirrors.tencent.com/iconv-lite/0.6.3
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
    optionalDependencies:
      dmg-license: mirrors.tencent.com/dmg-license/1.0.11
    transitivePeerDependencies:
      - electron-builder-squirrel-windows
      - supports-color
    dev: true

  mirrors.tencent.com/dmg-license/1.0.11:
    resolution: {integrity: sha512-ZdzmqwKmECOWJpqefloC5OJy1+WZBBse5+MR88z9g9Zn4VY+WYUkAyojmhzJckH5YbbZGcYIuGAkY5/Ys5OM2Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dmg-license/-/dmg-license-1.0.11.tgz}
    name: dmg-license
    version: 1.0.11
    engines: {node: '>=8'}
    os: [darwin]
    hasBin: true
    requiresBuild: true
    dependencies:
      '@types/plist': mirrors.tencent.com/@types/plist/3.0.5
      '@types/verror': mirrors.tencent.com/@types/verror/1.10.11
      ajv: mirrors.tencent.com/ajv/6.12.6
      crc: mirrors.tencent.com/crc/3.8.0
      iconv-corefoundation: mirrors.tencent.com/iconv-corefoundation/1.1.7
      plist: mirrors.tencent.com/plist/3.1.0
      smart-buffer: mirrors.tencent.com/smart-buffer/4.2.0
      verror: mirrors.tencent.com/verror/1.10.1
    dev: true
    optional: true

  mirrors.tencent.com/doctrine/3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/doctrine/-/doctrine-3.0.0.tgz}
    name: doctrine
    version: 3.0.0
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: mirrors.tencent.com/esutils/2.0.3
    dev: true

  mirrors.tencent.com/dom-helpers/5.2.1:
    resolution: {integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dom-helpers/-/dom-helpers-5.2.1.tgz}
    name: dom-helpers
    version: 5.2.1
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.26.10
      csstype: mirrors.tencent.com/csstype/3.1.3
    dev: true

  mirrors.tencent.com/dot-prop/6.0.1:
    resolution: {integrity: sha512-tE7ztYzXHIeyvc7N+hR3oi7FIbf/NIjVP9hmAt3yMXzrQ072/fpjGLx2GxNxGxUl5V73MEqYzioOMoVhGMJ5cA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dot-prop/-/dot-prop-6.0.1.tgz}
    name: dot-prop
    version: 6.0.1
    engines: {node: '>=10'}
    dependencies:
      is-obj: mirrors.tencent.com/is-obj/2.0.0
    dev: false

  mirrors.tencent.com/dotenv-expand/5.1.0:
    resolution: {integrity: sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dotenv-expand/-/dotenv-expand-5.1.0.tgz}
    name: dotenv-expand
    version: 5.1.0
    dev: true

  mirrors.tencent.com/dotenv/9.0.2:
    resolution: {integrity: sha512-I9OvvrHp4pIARv4+x9iuewrWycX6CcZtoAu1XrzPxc5UygMJXJZYmBsynku8IkrJwgypE5DGNjDPmPRhDCptUg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dotenv/-/dotenv-9.0.2.tgz}
    name: dotenv
    version: 9.0.2
    engines: {node: '>=10'}
    dev: true

  mirrors.tencent.com/dunder-proto/1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/dunder-proto/-/dunder-proto-1.0.1.tgz}
    name: dunder-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: mirrors.tencent.com/call-bind-apply-helpers/1.0.2
      es-errors: mirrors.tencent.com/es-errors/1.3.0
      gopd: mirrors.tencent.com/gopd/1.2.0

  mirrors.tencent.com/eastasianwidth/0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eastasianwidth/-/eastasianwidth-0.2.0.tgz}
    name: eastasianwidth
    version: 0.2.0
    dev: true

  mirrors.tencent.com/ecc-jsbn/0.1.2:
    resolution: {integrity: sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz}
    name: ecc-jsbn
    version: 0.1.2
    dependencies:
      jsbn: mirrors.tencent.com/jsbn/0.1.1
      safer-buffer: mirrors.tencent.com/safer-buffer/2.1.2
    dev: false

  mirrors.tencent.com/ejs/3.1.10:
    resolution: {integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ejs/-/ejs-3.1.10.tgz}
    name: ejs
    version: 3.1.10
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      jake: mirrors.tencent.com/jake/10.9.2
    dev: true

  mirrors.tencent.com/electron-builder/24.13.3:
    resolution: {integrity: sha512-yZSgVHft5dNVlo31qmJAe4BVKQfFdwpRw7sFp1iQglDRCDD6r22zfRJuZlhtB5gp9FHUxCMEoWGq10SkCnMAIg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/electron-builder/-/electron-builder-24.13.3.tgz}
    name: electron-builder
    version: 24.13.3
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      app-builder-lib: mirrors.tencent.com/app-builder-lib/24.13.3_dmg-builder@24.13.3
      builder-util: mirrors.tencent.com/builder-util/24.13.1
      builder-util-runtime: mirrors.tencent.com/builder-util-runtime/9.2.4
      chalk: mirrors.tencent.com/chalk/4.1.2
      dmg-builder: mirrors.tencent.com/dmg-builder/24.13.3
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      is-ci: mirrors.tencent.com/is-ci/3.0.1
      lazy-val: mirrors.tencent.com/lazy-val/1.0.5
      read-config-file: mirrors.tencent.com/read-config-file/6.3.2
      simple-update-notifier: mirrors.tencent.com/simple-update-notifier/2.0.0
      yargs: mirrors.tencent.com/yargs/17.7.2
    transitivePeerDependencies:
      - electron-builder-squirrel-windows
      - supports-color
    dev: true

  mirrors.tencent.com/electron-publish/24.13.1:
    resolution: {integrity: sha512-2ZgdEqJ8e9D17Hwp5LEq5mLQPjqU3lv/IALvgp+4W8VeNhryfGhYEQC/PgDPMrnWUp+l60Ou5SJLsu+k4mhQ8A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/electron-publish/-/electron-publish-24.13.1.tgz}
    name: electron-publish
    version: 24.13.1
    dependencies:
      '@types/fs-extra': mirrors.tencent.com/@types/fs-extra/9.0.13
      builder-util: mirrors.tencent.com/builder-util/24.13.1
      builder-util-runtime: mirrors.tencent.com/builder-util-runtime/9.2.4
      chalk: mirrors.tencent.com/chalk/4.1.2
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
      lazy-val: mirrors.tencent.com/lazy-val/1.0.5
      mime: mirrors.tencent.com/mime/2.6.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/electron/30.5.1:
    resolution: {integrity: sha512-AhL7+mZ8Lg14iaNfoYTkXQ2qee8mmsQyllKdqxlpv/zrKgfxz6jNVtcRRbQtLxtF8yzcImWdfTQROpYiPumdbw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/electron/-/electron-30.5.1.tgz}
    name: electron
    version: 30.5.1
    engines: {node: '>= 12.20.55'}
    hasBin: true
    requiresBuild: true
    dependencies:
      '@electron/get': mirrors.tencent.com/@electron/get/2.0.3
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      extract-zip: mirrors.tencent.com/extract-zip/2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  mirrors.tencent.com/emoji-regex/8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/emoji-regex/-/emoji-regex-8.0.0.tgz}
    name: emoji-regex
    version: 8.0.0
    dev: true

  mirrors.tencent.com/emoji-regex/9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/emoji-regex/-/emoji-regex-9.2.2.tgz}
    name: emoji-regex
    version: 9.2.2
    dev: true

  mirrors.tencent.com/end-of-stream/1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/end-of-stream/-/end-of-stream-1.4.4.tgz}
    name: end-of-stream
    version: 1.4.4
    dependencies:
      once: mirrors.tencent.com/once/1.4.0
    dev: false

  mirrors.tencent.com/entities/4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/entities/-/entities-4.5.0.tgz}
    name: entities
    version: 4.5.0
    engines: {node: '>=0.12'}
    dev: true

  mirrors.tencent.com/env-paths/2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/env-paths/-/env-paths-2.2.1.tgz}
    name: env-paths
    version: 2.2.1
    engines: {node: '>=6'}
    dev: false

  mirrors.tencent.com/err-code/2.0.3:
    resolution: {integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/err-code/-/err-code-2.0.3.tgz}
    name: err-code
    version: 2.0.3
    dev: true

  mirrors.tencent.com/error-ex/1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/error-ex/-/error-ex-1.3.2.tgz}
    name: error-ex
    version: 1.3.2
    dependencies:
      is-arrayish: mirrors.tencent.com/is-arrayish/0.2.1
    dev: true

  mirrors.tencent.com/es-define-property/1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/es-define-property/-/es-define-property-1.0.1.tgz}
    name: es-define-property
    version: 1.0.1
    engines: {node: '>= 0.4'}

  mirrors.tencent.com/es-errors/1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/es-errors/-/es-errors-1.3.0.tgz}
    name: es-errors
    version: 1.3.0
    engines: {node: '>= 0.4'}

  mirrors.tencent.com/es-object-atoms/1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/es-object-atoms/-/es-object-atoms-1.1.1.tgz}
    name: es-object-atoms
    version: 1.1.1
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: mirrors.tencent.com/es-errors/1.3.0

  mirrors.tencent.com/es-set-tostringtag/2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz}
    name: es-set-tostringtag
    version: 2.1.0
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: mirrors.tencent.com/es-errors/1.3.0
      get-intrinsic: mirrors.tencent.com/get-intrinsic/1.3.0
      has-tostringtag: mirrors.tencent.com/has-tostringtag/1.0.2
      hasown: mirrors.tencent.com/hasown/2.0.2

  mirrors.tencent.com/es6-error/4.1.1:
    resolution: {integrity: sha512-Um/+FxMr9CISWh0bi5Zv0iOD+4cFh5qLeks1qhAopKVAJw3drgKbKySikp7wGhDL0HPeaja0P5ULZrxLkniUVg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/es6-error/-/es6-error-4.1.1.tgz}
    name: es6-error
    version: 4.1.1
    dev: false
    optional: true

  mirrors.tencent.com/esbuild/0.21.5:
    resolution: {integrity: sha512-mg3OPMV4hXywwpoDxu3Qda5xCKQi+vCTZq8S9J/EpkhB2HzKXq4SNFZE3+NK93JYxc8VMSep+lOUSC/RVKaBqw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/esbuild/-/esbuild-0.21.5.tgz}
    name: esbuild
    version: 0.21.5
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/aix-ppc64': mirrors.tencent.com/@esbuild/aix-ppc64/0.21.5
      '@esbuild/android-arm': mirrors.tencent.com/@esbuild/android-arm/0.21.5
      '@esbuild/android-arm64': mirrors.tencent.com/@esbuild/android-arm64/0.21.5
      '@esbuild/android-x64': mirrors.tencent.com/@esbuild/android-x64/0.21.5
      '@esbuild/darwin-arm64': mirrors.tencent.com/@esbuild/darwin-arm64/0.21.5
      '@esbuild/darwin-x64': mirrors.tencent.com/@esbuild/darwin-x64/0.21.5
      '@esbuild/freebsd-arm64': mirrors.tencent.com/@esbuild/freebsd-arm64/0.21.5
      '@esbuild/freebsd-x64': mirrors.tencent.com/@esbuild/freebsd-x64/0.21.5
      '@esbuild/linux-arm': mirrors.tencent.com/@esbuild/linux-arm/0.21.5
      '@esbuild/linux-arm64': mirrors.tencent.com/@esbuild/linux-arm64/0.21.5
      '@esbuild/linux-ia32': mirrors.tencent.com/@esbuild/linux-ia32/0.21.5
      '@esbuild/linux-loong64': mirrors.tencent.com/@esbuild/linux-loong64/0.21.5
      '@esbuild/linux-mips64el': mirrors.tencent.com/@esbuild/linux-mips64el/0.21.5
      '@esbuild/linux-ppc64': mirrors.tencent.com/@esbuild/linux-ppc64/0.21.5
      '@esbuild/linux-riscv64': mirrors.tencent.com/@esbuild/linux-riscv64/0.21.5
      '@esbuild/linux-s390x': mirrors.tencent.com/@esbuild/linux-s390x/0.21.5
      '@esbuild/linux-x64': mirrors.tencent.com/@esbuild/linux-x64/0.21.5
      '@esbuild/netbsd-x64': mirrors.tencent.com/@esbuild/netbsd-x64/0.21.5
      '@esbuild/openbsd-x64': mirrors.tencent.com/@esbuild/openbsd-x64/0.21.5
      '@esbuild/sunos-x64': mirrors.tencent.com/@esbuild/sunos-x64/0.21.5
      '@esbuild/win32-arm64': mirrors.tencent.com/@esbuild/win32-arm64/0.21.5
      '@esbuild/win32-ia32': mirrors.tencent.com/@esbuild/win32-ia32/0.21.5
      '@esbuild/win32-x64': mirrors.tencent.com/@esbuild/win32-x64/0.21.5
    dev: true

  mirrors.tencent.com/escalade/3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/escalade/-/escalade-3.2.0.tgz}
    name: escalade
    version: 3.2.0
    engines: {node: '>=6'}
    dev: true

  mirrors.tencent.com/escape-string-regexp/4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    name: escape-string-regexp
    version: 4.0.0
    engines: {node: '>=10'}

  mirrors.tencent.com/eslint-plugin-react-hooks/4.6.2_eslint@8.57.1:
    resolution: {integrity: sha512-QzliNJq4GinDBcD8gPB5v0wh6g8q3SUi6EFF0x8N/BL9PoVs0atuGc47ozMRyOWAKdwaZ5OnbOEa3WR+dSGKuQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz}
    id: mirrors.tencent.com/eslint-plugin-react-hooks/4.6.2
    name: eslint-plugin-react-hooks
    version: 4.6.2
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
    dependencies:
      eslint: mirrors.tencent.com/eslint/8.57.1
    dev: true

  mirrors.tencent.com/eslint-plugin-react-refresh/0.4.20_eslint@8.57.1:
    resolution: {integrity: sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz}
    id: mirrors.tencent.com/eslint-plugin-react-refresh/0.4.20
    name: eslint-plugin-react-refresh
    version: 0.4.20
    peerDependencies:
      eslint: '>=8.40'
    dependencies:
      eslint: mirrors.tencent.com/eslint/8.57.1
    dev: true

  mirrors.tencent.com/eslint-scope/7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eslint-scope/-/eslint-scope-7.2.2.tgz}
    name: eslint-scope
    version: 7.2.2
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: mirrors.tencent.com/esrecurse/4.3.0
      estraverse: mirrors.tencent.com/estraverse/5.3.0
    dev: true

  mirrors.tencent.com/eslint-visitor-keys/3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    name: eslint-visitor-keys
    version: 3.4.3
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  mirrors.tencent.com/eslint/8.57.1:
    resolution: {integrity: sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/eslint/-/eslint-8.57.1.tgz}
    name: eslint
    version: 8.57.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true
    dependencies:
      '@eslint-community/eslint-utils': mirrors.tencent.com/@eslint-community/eslint-utils/4.7.0_eslint@8.57.1
      '@eslint-community/regexpp': mirrors.tencent.com/@eslint-community/regexpp/4.12.1
      '@eslint/eslintrc': mirrors.tencent.com/@eslint/eslintrc/2.1.4
      '@eslint/js': mirrors.tencent.com/@eslint/js/8.57.1
      '@humanwhocodes/config-array': mirrors.tencent.com/@humanwhocodes/config-array/0.13.0
      '@humanwhocodes/module-importer': mirrors.tencent.com/@humanwhocodes/module-importer/1.0.1
      '@nodelib/fs.walk': mirrors.tencent.com/@nodelib/fs.walk/1.2.8
      '@ungap/structured-clone': mirrors.tencent.com/@ungap/structured-clone/1.3.0
      ajv: mirrors.tencent.com/ajv/6.12.6
      chalk: mirrors.tencent.com/chalk/4.1.2
      cross-spawn: mirrors.tencent.com/cross-spawn/7.0.6
      debug: mirrors.tencent.com/debug/4.4.1
      doctrine: mirrors.tencent.com/doctrine/3.0.0
      escape-string-regexp: mirrors.tencent.com/escape-string-regexp/4.0.0
      eslint-scope: mirrors.tencent.com/eslint-scope/7.2.2
      eslint-visitor-keys: mirrors.tencent.com/eslint-visitor-keys/3.4.3
      espree: mirrors.tencent.com/espree/9.6.1
      esquery: mirrors.tencent.com/esquery/1.6.0
      esutils: mirrors.tencent.com/esutils/2.0.3
      fast-deep-equal: mirrors.tencent.com/fast-deep-equal/3.1.3
      file-entry-cache: mirrors.tencent.com/file-entry-cache/6.0.1
      find-up: mirrors.tencent.com/find-up/5.0.0
      glob-parent: mirrors.tencent.com/glob-parent/6.0.2
      globals: mirrors.tencent.com/globals/13.24.0
      graphemer: mirrors.tencent.com/graphemer/1.4.0
      ignore: mirrors.tencent.com/ignore/5.3.2
      imurmurhash: mirrors.tencent.com/imurmurhash/0.1.4
      is-glob: mirrors.tencent.com/is-glob/4.0.3
      is-path-inside: mirrors.tencent.com/is-path-inside/3.0.3
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
      json-stable-stringify-without-jsonify: mirrors.tencent.com/json-stable-stringify-without-jsonify/1.0.1
      levn: mirrors.tencent.com/levn/0.4.1
      lodash.merge: mirrors.tencent.com/lodash.merge/4.6.2
      minimatch: mirrors.tencent.com/minimatch/3.1.2
      natural-compare: mirrors.tencent.com/natural-compare/1.4.0
      optionator: mirrors.tencent.com/optionator/0.9.4
      strip-ansi: mirrors.tencent.com/strip-ansi/6.0.1
      text-table: mirrors.tencent.com/text-table/0.2.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/espree/9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/espree/-/espree-9.6.1.tgz}
    name: espree
    version: 9.6.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: mirrors.tencent.com/acorn/8.15.0
      acorn-jsx: mirrors.tencent.com/acorn-jsx/5.3.2_acorn@8.15.0
      eslint-visitor-keys: mirrors.tencent.com/eslint-visitor-keys/3.4.3
    dev: true

  mirrors.tencent.com/esquery/1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/esquery/-/esquery-1.6.0.tgz}
    name: esquery
    version: 1.6.0
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: mirrors.tencent.com/estraverse/5.3.0
    dev: true

  mirrors.tencent.com/esrecurse/4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/esrecurse/-/esrecurse-4.3.0.tgz}
    name: esrecurse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: mirrors.tencent.com/estraverse/5.3.0
    dev: true

  mirrors.tencent.com/estraverse/5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/estraverse/-/estraverse-5.3.0.tgz}
    name: estraverse
    version: 5.3.0
    engines: {node: '>=4.0'}
    dev: true

  mirrors.tencent.com/estree-walker/2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2
    dev: true

  mirrors.tencent.com/esutils/2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/exsolve/1.0.5:
    resolution: {integrity: sha512-pz5dvkYYKQ1AHVrgOzBKWeP4u4FRb3a6DNK2ucr0OoNwYIU4QWsJ+NM36LLzORT+z845MzKHHhpXiUF5nvQoJg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/exsolve/-/exsolve-1.0.5.tgz}
    name: exsolve
    version: 1.0.5
    dev: true

  mirrors.tencent.com/extend/3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/extend/-/extend-3.0.2.tgz}
    name: extend
    version: 3.0.2
    dev: false

  mirrors.tencent.com/extract-zip/2.0.1:
    resolution: {integrity: sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/extract-zip/-/extract-zip-2.0.1.tgz}
    name: extract-zip
    version: 2.0.1
    engines: {node: '>= 10.17.0'}
    hasBin: true
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
      get-stream: mirrors.tencent.com/get-stream/5.2.0
      yauzl: mirrors.tencent.com/yauzl/2.10.0
    optionalDependencies:
      '@types/yauzl': mirrors.tencent.com/@types/yauzl/2.10.3
    transitivePeerDependencies:
      - supports-color
    dev: false

  mirrors.tencent.com/extsprintf/1.3.0:
    resolution: {integrity: sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/extsprintf/-/extsprintf-1.3.0.tgz}
    name: extsprintf
    version: 1.3.0
    engines: {'0': node >=0.6.0}
    dev: false

  mirrors.tencent.com/extsprintf/1.4.1:
    resolution: {integrity: sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/extsprintf/-/extsprintf-1.4.1.tgz}
    name: extsprintf
    version: 1.4.1
    engines: {'0': node >=0.6.0}
    dev: true
    optional: true

  mirrors.tencent.com/fast-deep-equal/3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    name: fast-deep-equal
    version: 3.1.3

  mirrors.tencent.com/fast-glob/3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-glob/-/fast-glob-3.3.3.tgz}
    name: fast-glob
    version: 3.3.3
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': mirrors.tencent.com/@nodelib/fs.stat/2.0.5
      '@nodelib/fs.walk': mirrors.tencent.com/@nodelib/fs.walk/1.2.8
      glob-parent: mirrors.tencent.com/glob-parent/5.1.2
      merge2: mirrors.tencent.com/merge2/1.4.1
      micromatch: mirrors.tencent.com/micromatch/4.0.8
    dev: true

  mirrors.tencent.com/fast-json-stable-stringify/2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}
    name: fast-json-stable-stringify
    version: 2.1.0

  mirrors.tencent.com/fast-levenshtein/2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: true

  mirrors.tencent.com/fast-xml-parser/4.2.5:
    resolution: {integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-xml-parser/-/fast-xml-parser-4.2.5.tgz}
    name: fast-xml-parser
    version: 4.2.5
    hasBin: true
    dependencies:
      strnum: mirrors.tencent.com/strnum/1.1.2
    dev: false

  mirrors.tencent.com/fast-xml-parser/4.5.0:
    resolution: {integrity: sha512-/PlTQCI96+fZMAOLMZK4CWG1ItCbfZ/0jx7UIJFChPNrx7tcEgerUgWbeieCM9MfHInUDyK8DWYZ+YrywDJuTg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fast-xml-parser/-/fast-xml-parser-4.5.0.tgz}
    name: fast-xml-parser
    version: 4.5.0
    hasBin: true
    dependencies:
      strnum: mirrors.tencent.com/strnum/1.1.2
    dev: true

  mirrors.tencent.com/fastq/1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fastq/-/fastq-1.19.1.tgz}
    name: fastq
    version: 1.19.1
    dependencies:
      reusify: mirrors.tencent.com/reusify/1.1.0
    dev: true

  mirrors.tencent.com/fd-slicer/1.1.0:
    resolution: {integrity: sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fd-slicer/-/fd-slicer-1.1.0.tgz}
    name: fd-slicer
    version: 1.1.0
    dependencies:
      pend: mirrors.tencent.com/pend/1.2.0
    dev: false

  mirrors.tencent.com/file-entry-cache/6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/file-entry-cache/-/file-entry-cache-6.0.1.tgz}
    name: file-entry-cache
    version: 6.0.1
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: mirrors.tencent.com/flat-cache/3.2.0
    dev: true

  mirrors.tencent.com/filelist/1.0.4:
    resolution: {integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/filelist/-/filelist-1.0.4.tgz}
    name: filelist
    version: 1.0.4
    dependencies:
      minimatch: mirrors.tencent.com/minimatch/5.1.6
    dev: true

  mirrors.tencent.com/filesize/10.1.6:
    resolution: {integrity: sha512-sJslQKU2uM33qH5nqewAwVB2QgR6w1aMNsYUp3aN5rMRyXEwJGmZvaWzeJFNTOXWlHQyBFCWrdj3fV/fsTOX8w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/filesize/-/filesize-10.1.6.tgz}
    name: filesize
    version: 10.1.6
    engines: {node: '>= 10.4.0'}
    dev: true

  mirrors.tencent.com/fill-range/7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fill-range/-/fill-range-7.1.1.tgz}
    name: fill-range
    version: 7.1.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: mirrors.tencent.com/to-regex-range/5.0.1
    dev: true

  mirrors.tencent.com/find-root/1.1.0:
    resolution: {integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/find-root/-/find-root-1.1.0.tgz}
    name: find-root
    version: 1.1.0
    dev: true

  mirrors.tencent.com/find-up/3.0.0:
    resolution: {integrity: sha512-1yD6RmLI1XBfxugvORwlck6f75tYL+iR0jqwsOrOxMZyGYqUuDhJ0l4AXdO1iX/FTs9cBAMEk1gWSEx1kSbylg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/find-up/-/find-up-3.0.0.tgz}
    name: find-up
    version: 3.0.0
    engines: {node: '>=6'}
    dependencies:
      locate-path: mirrors.tencent.com/locate-path/3.0.0
    dev: false

  mirrors.tencent.com/find-up/5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/find-up/-/find-up-5.0.0.tgz}
    name: find-up
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      locate-path: mirrors.tencent.com/locate-path/6.0.0
      path-exists: mirrors.tencent.com/path-exists/4.0.0
    dev: true

  mirrors.tencent.com/flat-cache/3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/flat-cache/-/flat-cache-3.2.0.tgz}
    name: flat-cache
    version: 3.2.0
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: mirrors.tencent.com/flatted/3.3.3
      keyv: mirrors.tencent.com/keyv/4.5.4
      rimraf: mirrors.tencent.com/rimraf/3.0.2
    dev: true

  mirrors.tencent.com/flatted/3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/flatted/-/flatted-3.3.3.tgz}
    name: flatted
    version: 3.3.3
    dev: true

  mirrors.tencent.com/follow-redirects/1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/follow-redirects/-/follow-redirects-1.15.9.tgz}
    name: follow-redirects
    version: 1.15.9
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  mirrors.tencent.com/foreground-child/3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/foreground-child/-/foreground-child-3.3.1.tgz}
    name: foreground-child
    version: 3.3.1
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: mirrors.tencent.com/cross-spawn/7.0.6
      signal-exit: mirrors.tencent.com/signal-exit/4.1.0
    dev: true

  mirrors.tencent.com/forever-agent/0.6.1:
    resolution: {integrity: sha512-j0KLYPhm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/forever-agent/-/forever-agent-0.6.1.tgz}
    name: forever-agent
    version: 0.6.1
    dev: false

  mirrors.tencent.com/form-data/2.3.3:
    resolution: {integrity: sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/form-data/-/form-data-2.3.3.tgz}
    name: form-data
    version: 2.3.3
    engines: {node: '>= 0.12'}
    dependencies:
      asynckit: mirrors.tencent.com/asynckit/0.4.0
      combined-stream: mirrors.tencent.com/combined-stream/1.0.8
      mime-types: mirrors.tencent.com/mime-types/2.1.35
    dev: false

  mirrors.tencent.com/form-data/4.0.3:
    resolution: {integrity: sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/form-data/-/form-data-4.0.3.tgz}
    name: form-data
    version: 4.0.3
    engines: {node: '>= 6'}
    dependencies:
      asynckit: mirrors.tencent.com/asynckit/0.4.0
      combined-stream: mirrors.tencent.com/combined-stream/1.0.8
      es-set-tostringtag: mirrors.tencent.com/es-set-tostringtag/2.1.0
      hasown: mirrors.tencent.com/hasown/2.0.2
      mime-types: mirrors.tencent.com/mime-types/2.1.35

  mirrors.tencent.com/fs-extra/10.1.0:
    resolution: {integrity: sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-10.1.0.tgz}
    name: fs-extra
    version: 10.1.0
    engines: {node: '>=12'}
    dependencies:
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
      jsonfile: mirrors.tencent.com/jsonfile/6.1.0
      universalify: mirrors.tencent.com/universalify/2.0.1
    dev: true

  mirrors.tencent.com/fs-extra/11.3.0:
    resolution: {integrity: sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-11.3.0.tgz}
    name: fs-extra
    version: 11.3.0
    engines: {node: '>=14.14'}
    dependencies:
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
      jsonfile: mirrors.tencent.com/jsonfile/6.1.0
      universalify: mirrors.tencent.com/universalify/2.0.1
    dev: true

  mirrors.tencent.com/fs-extra/8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-8.1.0.tgz}
    name: fs-extra
    version: 8.1.0
    engines: {node: '>=6 <7 || >=8'}
    dependencies:
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
      jsonfile: mirrors.tencent.com/jsonfile/4.0.0
      universalify: mirrors.tencent.com/universalify/0.1.2
    dev: false

  mirrors.tencent.com/fs-extra/9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs-extra/-/fs-extra-9.1.0.tgz}
    name: fs-extra
    version: 9.1.0
    engines: {node: '>=10'}
    dependencies:
      at-least-node: mirrors.tencent.com/at-least-node/1.0.0
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
      jsonfile: mirrors.tencent.com/jsonfile/6.1.0
      universalify: mirrors.tencent.com/universalify/2.0.1
    dev: true

  mirrors.tencent.com/fs-minipass/2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs-minipass/-/fs-minipass-2.1.0.tgz}
    name: fs-minipass
    version: 2.1.0
    engines: {node: '>= 8'}
    dependencies:
      minipass: mirrors.tencent.com/minipass/3.3.6
    dev: true

  mirrors.tencent.com/fs.realpath/1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fs.realpath/-/fs.realpath-1.0.0.tgz}
    name: fs.realpath
    version: 1.0.0
    dev: true

  mirrors.tencent.com/fsevents/2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  mirrors.tencent.com/function-bind/1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/function-bind/-/function-bind-1.1.2.tgz}
    name: function-bind
    version: 1.1.2

  mirrors.tencent.com/get-caller-file/2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/get-caller-file/-/get-caller-file-2.0.5.tgz}
    name: get-caller-file
    version: 2.0.5
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  mirrors.tencent.com/get-intrinsic/1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/get-intrinsic/-/get-intrinsic-1.3.0.tgz}
    name: get-intrinsic
    version: 1.3.0
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: mirrors.tencent.com/call-bind-apply-helpers/1.0.2
      es-define-property: mirrors.tencent.com/es-define-property/1.0.1
      es-errors: mirrors.tencent.com/es-errors/1.3.0
      es-object-atoms: mirrors.tencent.com/es-object-atoms/1.1.1
      function-bind: mirrors.tencent.com/function-bind/1.1.2
      get-proto: mirrors.tencent.com/get-proto/1.0.1
      gopd: mirrors.tencent.com/gopd/1.2.0
      has-symbols: mirrors.tencent.com/has-symbols/1.1.0
      hasown: mirrors.tencent.com/hasown/2.0.2
      math-intrinsics: mirrors.tencent.com/math-intrinsics/1.1.0

  mirrors.tencent.com/get-proto/1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/get-proto/-/get-proto-1.0.1.tgz}
    name: get-proto
    version: 1.0.1
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: mirrors.tencent.com/dunder-proto/1.0.1
      es-object-atoms: mirrors.tencent.com/es-object-atoms/1.1.1

  mirrors.tencent.com/get-stream/5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/get-stream/-/get-stream-5.2.0.tgz}
    name: get-stream
    version: 5.2.0
    engines: {node: '>=8'}
    dependencies:
      pump: mirrors.tencent.com/pump/3.0.2
    dev: false

  mirrors.tencent.com/getpass/0.1.7:
    resolution: {integrity: sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/getpass/-/getpass-0.1.7.tgz}
    name: getpass
    version: 0.1.7
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
    dev: false

  mirrors.tencent.com/glob-parent/5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: mirrors.tencent.com/is-glob/4.0.3
    dev: true

  mirrors.tencent.com/glob-parent/6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/glob-parent/-/glob-parent-6.0.2.tgz}
    name: glob-parent
    version: 6.0.2
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: mirrors.tencent.com/is-glob/4.0.3
    dev: true

  mirrors.tencent.com/glob/10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/glob/-/glob-10.4.5.tgz}
    name: glob
    version: 10.4.5
    hasBin: true
    dependencies:
      foreground-child: mirrors.tencent.com/foreground-child/3.3.1
      jackspeak: mirrors.tencent.com/jackspeak/3.4.3
      minimatch: mirrors.tencent.com/minimatch/9.0.5
      minipass: mirrors.tencent.com/minipass/7.1.2
      package-json-from-dist: mirrors.tencent.com/package-json-from-dist/1.0.1
      path-scurry: mirrors.tencent.com/path-scurry/1.11.1
    dev: true

  mirrors.tencent.com/glob/7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/glob/-/glob-7.2.3.tgz}
    name: glob
    version: 7.2.3
    deprecated: Glob versions prior to v9 are no longer supported
    dependencies:
      fs.realpath: mirrors.tencent.com/fs.realpath/1.0.0
      inflight: mirrors.tencent.com/inflight/1.0.6
      inherits: mirrors.tencent.com/inherits/2.0.4
      minimatch: mirrors.tencent.com/minimatch/3.1.2
      once: mirrors.tencent.com/once/1.4.0
      path-is-absolute: mirrors.tencent.com/path-is-absolute/1.0.1
    dev: true

  mirrors.tencent.com/global-agent/3.0.0:
    resolution: {integrity: sha512-PT6XReJ+D07JvGoxQMkT6qji/jVNfX/h364XHZOWeRzy64sSFr+xJ5OX7LI3b4MPQzdL4H8Y8M0xzPpsVMwA8Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/global-agent/-/global-agent-3.0.0.tgz}
    name: global-agent
    version: 3.0.0
    engines: {node: '>=10.0'}
    requiresBuild: true
    dependencies:
      boolean: mirrors.tencent.com/boolean/3.2.0
      es6-error: mirrors.tencent.com/es6-error/4.1.1
      matcher: mirrors.tencent.com/matcher/3.0.0
      roarr: mirrors.tencent.com/roarr/2.15.4
      semver: mirrors.tencent.com/semver/7.7.2
      serialize-error: mirrors.tencent.com/serialize-error/7.0.1
    dev: false
    optional: true

  mirrors.tencent.com/globals/11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/globals/-/globals-11.12.0.tgz}
    name: globals
    version: 11.12.0
    engines: {node: '>=4'}
    dev: true

  mirrors.tencent.com/globals/13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/globals/-/globals-13.24.0.tgz}
    name: globals
    version: 13.24.0
    engines: {node: '>=8'}
    dependencies:
      type-fest: mirrors.tencent.com/type-fest/0.20.2
    dev: true

  mirrors.tencent.com/globalthis/1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/globalthis/-/globalthis-1.0.4.tgz}
    name: globalthis
    version: 1.0.4
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: mirrors.tencent.com/define-properties/1.2.1
      gopd: mirrors.tencent.com/gopd/1.2.0
    dev: false
    optional: true

  mirrors.tencent.com/globby/11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/globby/-/globby-11.1.0.tgz}
    name: globby
    version: 11.1.0
    engines: {node: '>=10'}
    dependencies:
      array-union: mirrors.tencent.com/array-union/2.1.0
      dir-glob: mirrors.tencent.com/dir-glob/3.0.1
      fast-glob: mirrors.tencent.com/fast-glob/3.3.3
      ignore: mirrors.tencent.com/ignore/5.3.2
      merge2: mirrors.tencent.com/merge2/1.4.1
      slash: mirrors.tencent.com/slash/3.0.0
    dev: true

  mirrors.tencent.com/gopd/1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/gopd/-/gopd-1.2.0.tgz}
    name: gopd
    version: 1.2.0
    engines: {node: '>= 0.4'}

  mirrors.tencent.com/got/11.8.6:
    resolution: {integrity: sha512-6tfZ91bOr7bOXnK7PRDCGBLa1H4U080YHNaAQ2KsMGlLEzRbk44nsZF2E1IeRc3vtJHPVbKCYgdFbaGO2ljd8g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/got/-/got-11.8.6.tgz}
    name: got
    version: 11.8.6
    engines: {node: '>=10.19.0'}
    dependencies:
      '@sindresorhus/is': mirrors.tencent.com/@sindresorhus/is/4.6.0
      '@szmarczak/http-timer': mirrors.tencent.com/@szmarczak/http-timer/4.0.6
      '@types/cacheable-request': mirrors.tencent.com/@types/cacheable-request/6.0.3
      '@types/responselike': mirrors.tencent.com/@types/responselike/1.0.3
      cacheable-lookup: mirrors.tencent.com/cacheable-lookup/5.0.4
      cacheable-request: mirrors.tencent.com/cacheable-request/7.0.4
      decompress-response: mirrors.tencent.com/decompress-response/6.0.0
      http2-wrapper: mirrors.tencent.com/http2-wrapper/1.0.3
      lowercase-keys: mirrors.tencent.com/lowercase-keys/2.0.0
      p-cancelable: mirrors.tencent.com/p-cancelable/2.1.1
      responselike: mirrors.tencent.com/responselike/2.0.1
    dev: false

  mirrors.tencent.com/graceful-fs/4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/graceful-fs/-/graceful-fs-4.2.11.tgz}
    name: graceful-fs
    version: 4.2.11

  mirrors.tencent.com/graphemer/1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/graphemer/-/graphemer-1.4.0.tgz}
    name: graphemer
    version: 1.4.0
    dev: true

  mirrors.tencent.com/har-schema/2.0.0:
    resolution: {integrity: sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/har-schema/-/har-schema-2.0.0.tgz}
    name: har-schema
    version: 2.0.0
    engines: {node: '>=4'}
    dev: false

  mirrors.tencent.com/har-validator/5.1.5:
    resolution: {integrity: sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/har-validator/-/har-validator-5.1.5.tgz}
    name: har-validator
    version: 5.1.5
    engines: {node: '>=6'}
    deprecated: this library is no longer supported
    dependencies:
      ajv: mirrors.tencent.com/ajv/6.12.6
      har-schema: mirrors.tencent.com/har-schema/2.0.0
    dev: false

  mirrors.tencent.com/has-flag/4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/has-property-descriptors/1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz}
    name: has-property-descriptors
    version: 1.0.2
    dependencies:
      es-define-property: mirrors.tencent.com/es-define-property/1.0.1
    dev: false
    optional: true

  mirrors.tencent.com/has-symbols/1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/has-symbols/-/has-symbols-1.1.0.tgz}
    name: has-symbols
    version: 1.1.0
    engines: {node: '>= 0.4'}

  mirrors.tencent.com/has-tostringtag/1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/has-tostringtag/-/has-tostringtag-1.0.2.tgz}
    name: has-tostringtag
    version: 1.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: mirrors.tencent.com/has-symbols/1.1.0

  mirrors.tencent.com/hasown/2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/hasown/-/hasown-2.0.2.tgz}
    name: hasown
    version: 2.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: mirrors.tencent.com/function-bind/1.1.2

  mirrors.tencent.com/he/1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/he/-/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: true

  mirrors.tencent.com/hoist-non-react-statics/3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz}
    name: hoist-non-react-statics
    version: 3.3.2
    dependencies:
      react-is: mirrors.tencent.com/react-is/16.13.1
    dev: true

  mirrors.tencent.com/hosted-git-info/4.1.0:
    resolution: {integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/hosted-git-info/-/hosted-git-info-4.1.0.tgz}
    name: hosted-git-info
    version: 4.1.0
    engines: {node: '>=10'}
    dependencies:
      lru-cache: mirrors.tencent.com/lru-cache/6.0.0
    dev: true

  mirrors.tencent.com/http-cache-semantics/4.2.0:
    resolution: {integrity: sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz}
    name: http-cache-semantics
    version: 4.2.0
    dev: false

  mirrors.tencent.com/http-proxy-agent/5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz}
    name: http-proxy-agent
    version: 5.0.0
    engines: {node: '>= 6'}
    dependencies:
      '@tootallnate/once': mirrors.tencent.com/@tootallnate/once/2.0.0
      agent-base: mirrors.tencent.com/agent-base/6.0.2
      debug: mirrors.tencent.com/debug/4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/http-signature/1.2.0:
    resolution: {integrity: sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/http-signature/-/http-signature-1.2.0.tgz}
    name: http-signature
    version: 1.2.0
    engines: {node: '>=0.8', npm: '>=1.3.7'}
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
      jsprim: mirrors.tencent.com/jsprim/1.4.2
      sshpk: mirrors.tencent.com/sshpk/1.18.0
    dev: false

  mirrors.tencent.com/http2-wrapper/1.0.3:
    resolution: {integrity: sha512-V+23sDMr12Wnz7iTcDeJr3O6AIxlnvT/bmaAAAP/Xda35C90p9599p0F1eHR/N1KILWSoWVAiOMFjBBXaXSMxg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/http2-wrapper/-/http2-wrapper-1.0.3.tgz}
    name: http2-wrapper
    version: 1.0.3
    engines: {node: '>=10.19.0'}
    dependencies:
      quick-lru: mirrors.tencent.com/quick-lru/5.1.1
      resolve-alpn: mirrors.tencent.com/resolve-alpn/1.2.1
    dev: false

  mirrors.tencent.com/https-proxy-agent/5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz}
    name: https-proxy-agent
    version: 5.0.1
    engines: {node: '>= 6'}
    dependencies:
      agent-base: mirrors.tencent.com/agent-base/6.0.2
      debug: mirrors.tencent.com/debug/4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  mirrors.tencent.com/iconv-corefoundation/1.1.7:
    resolution: {integrity: sha512-T10qvkw0zz4wnm560lOEg0PovVqUXuOFhhHAkixw8/sycy7TJt7v/RrkEKEQnAw2viPSJu6iAkErxnzR0g8PpQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/iconv-corefoundation/-/iconv-corefoundation-1.1.7.tgz}
    name: iconv-corefoundation
    version: 1.1.7
    engines: {node: ^8.11.2 || >=10}
    os: [darwin]
    dependencies:
      cli-truncate: mirrors.tencent.com/cli-truncate/2.1.0
      node-addon-api: mirrors.tencent.com/node-addon-api/1.7.2
    dev: true
    optional: true

  mirrors.tencent.com/iconv-lite/0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/iconv-lite/-/iconv-lite-0.6.3.tgz}
    name: iconv-lite
    version: 0.6.3
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: mirrors.tencent.com/safer-buffer/2.1.2
    dev: true

  mirrors.tencent.com/ieee754/1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ieee754/-/ieee754-1.2.1.tgz}
    name: ieee754
    version: 1.2.1
    dev: true
    optional: true

  mirrors.tencent.com/ignore/5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ignore/-/ignore-5.3.2.tgz}
    name: ignore
    version: 5.3.2
    engines: {node: '>= 4'}
    dev: true

  mirrors.tencent.com/import-fresh/3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/import-fresh/-/import-fresh-3.3.1.tgz}
    name: import-fresh
    version: 3.3.1
    engines: {node: '>=6'}
    dependencies:
      parent-module: mirrors.tencent.com/parent-module/1.0.1
      resolve-from: mirrors.tencent.com/resolve-from/4.0.0
    dev: true

  mirrors.tencent.com/import-lazy/4.0.0:
    resolution: {integrity: sha512-rKtvo6a868b5Hu3heneU+L4yEQ4jYKLtjpnPeUdK7h0yzXGmyBTypknlkCvHFBqfX9YlorEiMM6Dnq/5atfHkw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/import-lazy/-/import-lazy-4.0.0.tgz}
    name: import-lazy
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/imurmurhash/0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/imurmurhash/-/imurmurhash-0.1.4.tgz}
    name: imurmurhash
    version: 0.1.4
    engines: {node: '>=0.8.19'}
    dev: true

  mirrors.tencent.com/inflight/1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/inflight/-/inflight-1.0.6.tgz}
    name: inflight
    version: 1.0.6
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
    dependencies:
      once: mirrors.tencent.com/once/1.4.0
      wrappy: mirrors.tencent.com/wrappy/1.0.2
    dev: true

  mirrors.tencent.com/inherits/2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/inherits/-/inherits-2.0.4.tgz}
    name: inherits
    version: 2.0.4
    dev: true

  mirrors.tencent.com/intersection-observer/0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/intersection-observer/-/intersection-observer-0.12.2.tgz}
    name: intersection-observer
    version: 0.12.2
    dev: true

  mirrors.tencent.com/is-arrayish/0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-arrayish/-/is-arrayish-0.2.1.tgz}
    name: is-arrayish
    version: 0.2.1
    dev: true

  mirrors.tencent.com/is-ci/3.0.1:
    resolution: {integrity: sha512-ZYvCgrefwqoQ6yTyYUbQu64HsITZ3NfKX1lzaEYdkTDcfKzzCI/wthRRYKkdjHKFVgNiXKAKm65Zo1pk2as/QQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-ci/-/is-ci-3.0.1.tgz}
    name: is-ci
    version: 3.0.1
    hasBin: true
    dependencies:
      ci-info: mirrors.tencent.com/ci-info/3.9.0
    dev: true

  mirrors.tencent.com/is-core-module/2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-core-module/-/is-core-module-2.16.1.tgz}
    name: is-core-module
    version: 2.16.1
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: mirrors.tencent.com/hasown/2.0.2
    dev: true

  mirrors.tencent.com/is-extglob/2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/is-fullwidth-code-point/3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz}
    name: is-fullwidth-code-point
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/is-glob/4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: mirrors.tencent.com/is-extglob/2.1.1
    dev: true

  mirrors.tencent.com/is-number/7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  mirrors.tencent.com/is-obj/2.0.0:
    resolution: {integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-obj/-/is-obj-2.0.0.tgz}
    name: is-obj
    version: 2.0.0
    engines: {node: '>=8'}
    dev: false

  mirrors.tencent.com/is-path-inside/3.0.3:
    resolution: {integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-path-inside/-/is-path-inside-3.0.3.tgz}
    name: is-path-inside
    version: 3.0.3
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/is-typedarray/1.0.0:
    resolution: {integrity: sha512-cyA56iCMHAh5CdzjJIa4aohJyeO1YbwLi3Jc35MmRU6poroFjIGZzUzupGiRPOjgHg9TLu43xbpwXk523fMxKA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/is-typedarray/-/is-typedarray-1.0.0.tgz}
    name: is-typedarray
    version: 1.0.0
    dev: false

  mirrors.tencent.com/isbinaryfile/4.0.10:
    resolution: {integrity: sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/isbinaryfile/-/isbinaryfile-4.0.10.tgz}
    name: isbinaryfile
    version: 4.0.10
    engines: {node: '>= 8.0.0'}
    dev: true

  mirrors.tencent.com/isbinaryfile/5.0.4:
    resolution: {integrity: sha512-YKBKVkKhty7s8rxddb40oOkuP0NbaeXrQvLin6QMHL7Ypiy2RW9LwOVrVgZRyOrhQlayMd9t+D8yDy8MKFTSDQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/isbinaryfile/-/isbinaryfile-5.0.4.tgz}
    name: isbinaryfile
    version: 5.0.4
    engines: {node: '>= 18.0.0'}
    dev: true

  mirrors.tencent.com/isexe/2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0
    dev: true

  mirrors.tencent.com/isstream/0.1.2:
    resolution: {integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/isstream/-/isstream-0.1.2.tgz}
    name: isstream
    version: 0.1.2
    dev: false

  mirrors.tencent.com/jackspeak/3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jackspeak/-/jackspeak-3.4.3.tgz}
    name: jackspeak
    version: 3.4.3
    dependencies:
      '@isaacs/cliui': mirrors.tencent.com/@isaacs/cliui/8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': mirrors.tencent.com/@pkgjs/parseargs/0.11.0
    dev: true

  mirrors.tencent.com/jake/10.9.2:
    resolution: {integrity: sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jake/-/jake-10.9.2.tgz}
    name: jake
    version: 10.9.2
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      async: mirrors.tencent.com/async/3.2.6
      chalk: mirrors.tencent.com/chalk/4.1.2
      filelist: mirrors.tencent.com/filelist/1.0.4
      minimatch: mirrors.tencent.com/minimatch/3.1.2
    dev: true

  mirrors.tencent.com/jju/1.4.0:
    resolution: {integrity: sha512-8wb9Yw966OSxApiCt0K3yNJL8pnNeIv+OEq2YMidz4FKP6nonSRoOXc80iXY4JaN2FC11B9qsNmDsm+ZOfMROA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jju/-/jju-1.4.0.tgz}
    name: jju
    version: 1.4.0
    dev: true

  mirrors.tencent.com/js-cookie/3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/js-cookie/-/js-cookie-3.0.5.tgz}
    name: js-cookie
    version: 3.0.5
    engines: {node: '>=14'}
    dev: true

  mirrors.tencent.com/js-tokens/4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0
    dev: true

  mirrors.tencent.com/js-yaml/4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/js-yaml/-/js-yaml-4.1.0.tgz}
    name: js-yaml
    version: 4.1.0
    hasBin: true
    dependencies:
      argparse: mirrors.tencent.com/argparse/2.0.1
    dev: true

  mirrors.tencent.com/jsbn/0.1.1:
    resolution: {integrity: sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jsbn/-/jsbn-0.1.1.tgz}
    name: jsbn
    version: 0.1.1
    dev: false

  mirrors.tencent.com/jsesc/3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jsesc/-/jsesc-3.1.0.tgz}
    name: jsesc
    version: 3.1.0
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  mirrors.tencent.com/json-buffer/3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-buffer/-/json-buffer-3.0.1.tgz}
    name: json-buffer
    version: 3.0.1

  mirrors.tencent.com/json-parse-even-better-errors/2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz}
    name: json-parse-even-better-errors
    version: 2.3.1
    dev: true

  mirrors.tencent.com/json-schema-traverse/0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}
    name: json-schema-traverse
    version: 0.4.1

  mirrors.tencent.com/json-schema-traverse/1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz}
    name: json-schema-traverse
    version: 1.0.0

  mirrors.tencent.com/json-schema-typed/7.0.3:
    resolution: {integrity: sha512-7DE8mpG+/fVw+dTpjbxnx47TaMnDfOI1jwft9g1VybltZCduyRQPJPvc+zzKY9WPHxhPWczyFuYa6I8Mw4iU5A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-schema-typed/-/json-schema-typed-7.0.3.tgz}
    name: json-schema-typed
    version: 7.0.3
    dev: false

  mirrors.tencent.com/json-schema/0.4.0:
    resolution: {integrity: sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-schema/-/json-schema-0.4.0.tgz}
    name: json-schema
    version: 0.4.0
    dev: false

  mirrors.tencent.com/json-stable-stringify-without-jsonify/1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}
    name: json-stable-stringify-without-jsonify
    version: 1.0.1
    dev: true

  mirrors.tencent.com/json-stringify-safe/5.0.1:
    resolution: {integrity: sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz}
    name: json-stringify-safe
    version: 5.0.1
    dev: false

  mirrors.tencent.com/json5/2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/json5/-/json5-2.2.3.tgz}
    name: json5
    version: 2.2.3
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  mirrors.tencent.com/jsonfile/4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jsonfile/-/jsonfile-4.0.0.tgz}
    name: jsonfile
    version: 4.0.0
    optionalDependencies:
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
    dev: false

  mirrors.tencent.com/jsonfile/6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jsonfile/-/jsonfile-6.1.0.tgz}
    name: jsonfile
    version: 6.1.0
    dependencies:
      universalify: mirrors.tencent.com/universalify/2.0.1
    optionalDependencies:
      graceful-fs: mirrors.tencent.com/graceful-fs/4.2.11
    dev: true

  mirrors.tencent.com/jsprim/1.4.2:
    resolution: {integrity: sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/jsprim/-/jsprim-1.4.2.tgz}
    name: jsprim
    version: 1.4.2
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
      extsprintf: mirrors.tencent.com/extsprintf/1.3.0
      json-schema: mirrors.tencent.com/json-schema/0.4.0
      verror: mirrors.tencent.com/verror/1.10.0
    dev: false

  mirrors.tencent.com/keyv/4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/keyv/-/keyv-4.5.4.tgz}
    name: keyv
    version: 4.5.4
    dependencies:
      json-buffer: mirrors.tencent.com/json-buffer/3.0.1

  mirrors.tencent.com/kolorist/1.8.0:
    resolution: {integrity: sha512-Y+60/zizpJ3HRH8DCss+q95yr6145JXZo46OTpFvDZWLfRCE4qChOyk1b26nMaNpfHHgxagk9dXT5OP0Tfe+dQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/kolorist/-/kolorist-1.8.0.tgz}
    name: kolorist
    version: 1.8.0
    dev: true

  mirrors.tencent.com/lazy-val/1.0.5:
    resolution: {integrity: sha512-0/BnGCCfyUMkBpeDgWihanIAF9JmZhHBgUhEqzvf+adhNGLoP6TaiI5oF8oyb3I45P+PcnrqihSf01M0l0G5+Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lazy-val/-/lazy-val-1.0.5.tgz}
    name: lazy-val
    version: 1.0.5
    dev: true

  mirrors.tencent.com/levn/0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/levn/-/levn-0.4.1.tgz}
    name: levn
    version: 0.4.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: mirrors.tencent.com/prelude-ls/1.2.1
      type-check: mirrors.tencent.com/type-check/0.4.0
    dev: true

  mirrors.tencent.com/lines-and-columns/1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lines-and-columns/-/lines-and-columns-1.2.4.tgz}
    name: lines-and-columns
    version: 1.2.4
    dev: true

  mirrors.tencent.com/local-pkg/1.1.1:
    resolution: {integrity: sha512-WunYko2W1NcdfAFpuLUoucsgULmgDBRkdxHxWQ7mK0cQqwPiy8E1enjuRBrhLtZkB5iScJ1XIPdhVEFK8aOLSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/local-pkg/-/local-pkg-1.1.1.tgz}
    name: local-pkg
    version: 1.1.1
    engines: {node: '>=14'}
    dependencies:
      mlly: mirrors.tencent.com/mlly/1.7.4
      pkg-types: mirrors.tencent.com/pkg-types/2.1.0
      quansync: mirrors.tencent.com/quansync/0.2.10
    dev: true

  mirrors.tencent.com/locate-path/3.0.0:
    resolution: {integrity: sha512-7AO748wWnIhNqAuaty2ZWHkQHRSNfPVIsPIfwEOWO22AmaoVrWavlOcMR5nzTLNYvp36X220/maaRsrec1G65A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/locate-path/-/locate-path-3.0.0.tgz}
    name: locate-path
    version: 3.0.0
    engines: {node: '>=6'}
    dependencies:
      p-locate: mirrors.tencent.com/p-locate/3.0.0
      path-exists: mirrors.tencent.com/path-exists/3.0.0
    dev: false

  mirrors.tencent.com/locate-path/6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/locate-path/-/locate-path-6.0.0.tgz}
    name: locate-path
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      p-locate: mirrors.tencent.com/p-locate/5.0.0
    dev: true

  mirrors.tencent.com/lodash-es/4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lodash-es/-/lodash-es-4.17.21.tgz}
    name: lodash-es
    version: 4.17.21
    dev: true

  mirrors.tencent.com/lodash.merge/4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lodash.merge/-/lodash.merge-4.6.2.tgz}
    name: lodash.merge
    version: 4.6.2
    dev: true

  mirrors.tencent.com/lodash/4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21
    dev: true

  mirrors.tencent.com/loose-envify/1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/loose-envify/-/loose-envify-1.4.0.tgz}
    name: loose-envify
    version: 1.4.0
    hasBin: true
    dependencies:
      js-tokens: mirrors.tencent.com/js-tokens/4.0.0
    dev: true

  mirrors.tencent.com/lowercase-keys/2.0.0:
    resolution: {integrity: sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lowercase-keys/-/lowercase-keys-2.0.0.tgz}
    name: lowercase-keys
    version: 2.0.0
    engines: {node: '>=8'}
    dev: false

  mirrors.tencent.com/lru-cache/10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-10.4.3.tgz}
    name: lru-cache
    version: 10.4.3
    dev: true

  mirrors.tencent.com/lru-cache/6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/lru-cache/-/lru-cache-6.0.0.tgz}
    name: lru-cache
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      yallist: mirrors.tencent.com/yallist/4.0.0
    dev: true

  mirrors.tencent.com/magic-string/0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/magic-string/-/magic-string-0.30.17.tgz}
    name: magic-string
    version: 0.30.17
    dependencies:
      '@jridgewell/sourcemap-codec': mirrors.tencent.com/@jridgewell/sourcemap-codec/1.5.0
    dev: true

  mirrors.tencent.com/make-dir/3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/make-dir/-/make-dir-3.1.0.tgz}
    name: make-dir
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      semver: mirrors.tencent.com/semver/6.3.1
    dev: false

  mirrors.tencent.com/matcher/3.0.0:
    resolution: {integrity: sha512-OkeDaAZ/bQCxeFAozM55PKcKU0yJMPGifLwV4Qgjitu+5MoAfSQN4lsLJeXZ1b8w0x+/Emda6MZgXS1jvsapng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/matcher/-/matcher-3.0.0.tgz}
    name: matcher
    version: 3.0.0
    engines: {node: '>=10'}
    dependencies:
      escape-string-regexp: mirrors.tencent.com/escape-string-regexp/4.0.0
    dev: false
    optional: true

  mirrors.tencent.com/math-intrinsics/1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/math-intrinsics/-/math-intrinsics-1.1.0.tgz}
    name: math-intrinsics
    version: 1.1.0
    engines: {node: '>= 0.4'}

  mirrors.tencent.com/merge2/1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  mirrors.tencent.com/micromatch/4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/micromatch/-/micromatch-4.0.8.tgz}
    name: micromatch
    version: 4.0.8
    engines: {node: '>=8.6'}
    dependencies:
      braces: mirrors.tencent.com/braces/3.0.3
      picomatch: mirrors.tencent.com/picomatch/2.3.1
    dev: true

  mirrors.tencent.com/mime-db/1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mime-db/-/mime-db-1.52.0.tgz}
    name: mime-db
    version: 1.52.0
    engines: {node: '>= 0.6'}

  mirrors.tencent.com/mime-types/2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mime-types/-/mime-types-2.1.35.tgz}
    name: mime-types
    version: 2.1.35
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: mirrors.tencent.com/mime-db/1.52.0

  mirrors.tencent.com/mime/2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mime/-/mime-2.6.0.tgz}
    name: mime
    version: 2.6.0
    engines: {node: '>=4.0.0'}
    hasBin: true
    dev: true

  mirrors.tencent.com/mimic-fn/2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mimic-fn/-/mimic-fn-2.1.0.tgz}
    name: mimic-fn
    version: 2.1.0
    engines: {node: '>=6'}
    dev: false

  mirrors.tencent.com/mimic-fn/3.1.0:
    resolution: {integrity: sha512-Ysbi9uYW9hFyfrThdDEQuykN4Ey6BuwPD2kpI5ES/nFTDn/98yxYNLZJcgUAKPT/mcrLLKaGzJR9YVxJrIdASQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mimic-fn/-/mimic-fn-3.1.0.tgz}
    name: mimic-fn
    version: 3.1.0
    engines: {node: '>=8'}
    dev: false

  mirrors.tencent.com/mimic-response/1.0.1:
    resolution: {integrity: sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mimic-response/-/mimic-response-1.0.1.tgz}
    name: mimic-response
    version: 1.0.1
    engines: {node: '>=4'}
    dev: false

  mirrors.tencent.com/mimic-response/3.1.0:
    resolution: {integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mimic-response/-/mimic-response-3.1.0.tgz}
    name: mimic-response
    version: 3.1.0
    engines: {node: '>=10'}
    dev: false

  mirrors.tencent.com/minimatch/3.0.8:
    resolution: {integrity: sha512-6FsRAQsxQ61mw+qP1ZzbL9Bc78x2p5OqNgNpnoAFLTrX8n5Kxph0CsnhmKKNXTWjXqU5L0pGPR7hYk+XWZr60Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimatch/-/minimatch-3.0.8.tgz}
    name: minimatch
    version: 3.0.8
    dependencies:
      brace-expansion: mirrors.tencent.com/brace-expansion/1.1.11
    dev: true

  mirrors.tencent.com/minimatch/3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: mirrors.tencent.com/brace-expansion/1.1.11
    dev: true

  mirrors.tencent.com/minimatch/5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimatch/-/minimatch-5.1.6.tgz}
    name: minimatch
    version: 5.1.6
    engines: {node: '>=10'}
    dependencies:
      brace-expansion: mirrors.tencent.com/brace-expansion/2.0.1
    dev: true

  mirrors.tencent.com/minimatch/9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimatch/-/minimatch-9.0.3.tgz}
    name: minimatch
    version: 9.0.3
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: mirrors.tencent.com/brace-expansion/2.0.1
    dev: true

  mirrors.tencent.com/minimatch/9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimatch/-/minimatch-9.0.5.tgz}
    name: minimatch
    version: 9.0.5
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: mirrors.tencent.com/brace-expansion/2.0.1
    dev: true

  mirrors.tencent.com/minimist/1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minimist/-/minimist-1.2.8.tgz}
    name: minimist
    version: 1.2.8
    dev: true

  mirrors.tencent.com/minipass/3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minipass/-/minipass-3.3.6.tgz}
    name: minipass
    version: 3.3.6
    engines: {node: '>=8'}
    dependencies:
      yallist: mirrors.tencent.com/yallist/4.0.0
    dev: true

  mirrors.tencent.com/minipass/5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minipass/-/minipass-5.0.0.tgz}
    name: minipass
    version: 5.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/minipass/7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minipass/-/minipass-7.1.2.tgz}
    name: minipass
    version: 7.1.2
    engines: {node: '>=16 || 14 >=14.17'}
    dev: true

  mirrors.tencent.com/minizlib/2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/minizlib/-/minizlib-2.1.2.tgz}
    name: minizlib
    version: 2.1.2
    engines: {node: '>= 8'}
    dependencies:
      minipass: mirrors.tencent.com/minipass/3.3.6
      yallist: mirrors.tencent.com/yallist/4.0.0
    dev: true

  mirrors.tencent.com/mitt/3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mitt/-/mitt-3.0.1.tgz}
    name: mitt
    version: 3.0.1
    dev: true

  mirrors.tencent.com/mkdirp/1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mkdirp/-/mkdirp-1.0.4.tgz}
    name: mkdirp
    version: 1.0.4
    engines: {node: '>=10'}
    hasBin: true
    dev: true

  mirrors.tencent.com/mlly/1.7.4:
    resolution: {integrity: sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/mlly/-/mlly-1.7.4.tgz}
    name: mlly
    version: 1.7.4
    dependencies:
      acorn: mirrors.tencent.com/acorn/8.15.0
      pathe: mirrors.tencent.com/pathe/2.0.3
      pkg-types: mirrors.tencent.com/pkg-types/1.3.1
      ufo: mirrors.tencent.com/ufo/1.6.1
    dev: true

  mirrors.tencent.com/ms/2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ms/-/ms-2.1.3.tgz}
    name: ms
    version: 2.1.3

  mirrors.tencent.com/muggle-string/0.4.1:
    resolution: {integrity: sha512-VNTrAak/KhO2i8dqqnqnAHOa3cYBwXEZe9h+D5h/1ZqFSTEFHdM65lR7RoIqq3tBBYavsOXV84NoHXZ0AkPyqQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/muggle-string/-/muggle-string-0.4.1.tgz}
    name: muggle-string
    version: 0.4.1
    dev: true

  mirrors.tencent.com/nanoid/3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/nanoid/-/nanoid-3.3.11.tgz}
    name: nanoid
    version: 3.3.11
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  mirrors.tencent.com/natural-compare/1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/natural-compare/-/natural-compare-1.4.0.tgz}
    name: natural-compare
    version: 1.4.0
    dev: true

  mirrors.tencent.com/node-addon-api/1.7.2:
    resolution: {integrity: sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/node-addon-api/-/node-addon-api-1.7.2.tgz}
    name: node-addon-api
    version: 1.7.2
    dev: true
    optional: true

  mirrors.tencent.com/normalize-url/6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/normalize-url/-/normalize-url-6.1.0.tgz}
    name: normalize-url
    version: 6.1.0
    engines: {node: '>=10'}
    dev: false

  mirrors.tencent.com/oauth-sign/0.9.0:
    resolution: {integrity: sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/oauth-sign/-/oauth-sign-0.9.0.tgz}
    name: oauth-sign
    version: 0.9.0
    dev: false

  mirrors.tencent.com/object-assign/4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/object-assign/-/object-assign-4.1.1.tgz}
    name: object-assign
    version: 4.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/object-keys/1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/object-keys/-/object-keys-1.1.1.tgz}
    name: object-keys
    version: 1.1.1
    engines: {node: '>= 0.4'}
    dev: false
    optional: true

  mirrors.tencent.com/once/1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/once/-/once-1.4.0.tgz}
    name: once
    version: 1.4.0
    dependencies:
      wrappy: mirrors.tencent.com/wrappy/1.0.2

  mirrors.tencent.com/onetime/5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/onetime/-/onetime-5.1.2.tgz}
    name: onetime
    version: 5.1.2
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: mirrors.tencent.com/mimic-fn/2.1.0
    dev: false

  mirrors.tencent.com/optionator/0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/optionator/-/optionator-0.9.4.tgz}
    name: optionator
    version: 0.9.4
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: mirrors.tencent.com/deep-is/0.1.4
      fast-levenshtein: mirrors.tencent.com/fast-levenshtein/2.0.6
      levn: mirrors.tencent.com/levn/0.4.1
      prelude-ls: mirrors.tencent.com/prelude-ls/1.2.1
      type-check: mirrors.tencent.com/type-check/0.4.0
      word-wrap: mirrors.tencent.com/word-wrap/1.2.5
    dev: true

  mirrors.tencent.com/p-cancelable/2.1.1:
    resolution: {integrity: sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-cancelable/-/p-cancelable-2.1.1.tgz}
    name: p-cancelable
    version: 2.1.1
    engines: {node: '>=8'}
    dev: false

  mirrors.tencent.com/p-limit/2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-limit/-/p-limit-2.3.0.tgz}
    name: p-limit
    version: 2.3.0
    engines: {node: '>=6'}
    dependencies:
      p-try: mirrors.tencent.com/p-try/2.2.0
    dev: false

  mirrors.tencent.com/p-limit/3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-limit/-/p-limit-3.1.0.tgz}
    name: p-limit
    version: 3.1.0
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: mirrors.tencent.com/yocto-queue/0.1.0
    dev: true

  mirrors.tencent.com/p-locate/3.0.0:
    resolution: {integrity: sha512-x+12w/To+4GFfgJhBEpiDcLozRJGegY+Ei7/z0tSLkMmxGZNybVMSfWj9aJn8Z5Fc7dBUNJOOVgPv2H7IwulSQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-locate/-/p-locate-3.0.0.tgz}
    name: p-locate
    version: 3.0.0
    engines: {node: '>=6'}
    dependencies:
      p-limit: mirrors.tencent.com/p-limit/2.3.0
    dev: false

  mirrors.tencent.com/p-locate/5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-locate/-/p-locate-5.0.0.tgz}
    name: p-locate
    version: 5.0.0
    engines: {node: '>=10'}
    dependencies:
      p-limit: mirrors.tencent.com/p-limit/3.1.0
    dev: true

  mirrors.tencent.com/p-try/2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/p-try/-/p-try-2.2.0.tgz}
    name: p-try
    version: 2.2.0
    engines: {node: '>=6'}
    dev: false

  mirrors.tencent.com/package-json-from-dist/1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz}
    name: package-json-from-dist
    version: 1.0.1
    dev: true

  mirrors.tencent.com/parent-module/1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/parent-module/-/parent-module-1.0.1.tgz}
    name: parent-module
    version: 1.0.1
    engines: {node: '>=6'}
    dependencies:
      callsites: mirrors.tencent.com/callsites/3.1.0
    dev: true

  mirrors.tencent.com/parse-json/5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/parse-json/-/parse-json-5.2.0.tgz}
    name: parse-json
    version: 5.2.0
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': mirrors.tencent.com/@babel/code-frame/7.27.1
      error-ex: mirrors.tencent.com/error-ex/1.3.2
      json-parse-even-better-errors: mirrors.tencent.com/json-parse-even-better-errors/2.3.1
      lines-and-columns: mirrors.tencent.com/lines-and-columns/1.2.4
    dev: true

  mirrors.tencent.com/path-browserify/1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-browserify/-/path-browserify-1.0.1.tgz}
    name: path-browserify
    version: 1.0.1
    dev: true

  mirrors.tencent.com/path-exists/3.0.0:
    resolution: {integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-exists/-/path-exists-3.0.0.tgz}
    name: path-exists
    version: 3.0.0
    engines: {node: '>=4'}
    dev: false

  mirrors.tencent.com/path-exists/4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/path-is-absolute/1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-is-absolute/-/path-is-absolute-1.0.1.tgz}
    name: path-is-absolute
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/path-key/3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/path-parse/1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-parse/-/path-parse-1.0.7.tgz}
    name: path-parse
    version: 1.0.7
    dev: true

  mirrors.tencent.com/path-scurry/1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-scurry/-/path-scurry-1.11.1.tgz}
    name: path-scurry
    version: 1.11.1
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: mirrors.tencent.com/lru-cache/10.4.3
      minipass: mirrors.tencent.com/minipass/7.1.2
    dev: true

  mirrors.tencent.com/path-type/4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/path-type/-/path-type-4.0.0.tgz}
    name: path-type
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/pathe/2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pathe/-/pathe-2.0.3.tgz}
    name: pathe
    version: 2.0.3
    dev: true

  mirrors.tencent.com/pend/1.2.0:
    resolution: {integrity: sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pend/-/pend-1.2.0.tgz}
    name: pend
    version: 1.2.0
    dev: false

  mirrors.tencent.com/performance-now/2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/performance-now/-/performance-now-2.1.0.tgz}
    name: performance-now
    version: 2.1.0

  mirrors.tencent.com/picocolors/1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/picocolors/-/picocolors-1.1.1.tgz}
    name: picocolors
    version: 1.1.1
    dev: true

  mirrors.tencent.com/picomatch/2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  mirrors.tencent.com/picomatch/4.0.2:
    resolution: {integrity: sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/picomatch/-/picomatch-4.0.2.tgz}
    name: picomatch
    version: 4.0.2
    engines: {node: '>=12'}
    dev: true

  mirrors.tencent.com/pkg-types/1.3.1:
    resolution: {integrity: sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pkg-types/-/pkg-types-1.3.1.tgz}
    name: pkg-types
    version: 1.3.1
    dependencies:
      confbox: mirrors.tencent.com/confbox/0.1.8
      mlly: mirrors.tencent.com/mlly/1.7.4
      pathe: mirrors.tencent.com/pathe/2.0.3
    dev: true

  mirrors.tencent.com/pkg-types/2.1.0:
    resolution: {integrity: sha512-wmJwA+8ihJixSoHKxZJRBQG1oY8Yr9pGLzRmSsNms0iNWyHHAlZCa7mmKiFR10YPZuz/2k169JiS/inOjBCZ2A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pkg-types/-/pkg-types-2.1.0.tgz}
    name: pkg-types
    version: 2.1.0
    dependencies:
      confbox: mirrors.tencent.com/confbox/0.2.2
      exsolve: mirrors.tencent.com/exsolve/1.0.5
      pathe: mirrors.tencent.com/pathe/2.0.3
    dev: true

  mirrors.tencent.com/pkg-up/3.1.0:
    resolution: {integrity: sha512-nDywThFk1i4BQK4twPQ6TA4RT8bDY96yeuCVBWL3ePARCiEKDRSrNGbFIgUJpLp+XeIR65v8ra7WuJOFUBtkMA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pkg-up/-/pkg-up-3.1.0.tgz}
    name: pkg-up
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      find-up: mirrors.tencent.com/find-up/3.0.0
    dev: false

  mirrors.tencent.com/plist/3.1.0:
    resolution: {integrity: sha512-uysumyrvkUX0rX/dEVqt8gC3sTBzd4zoWfLeS29nb53imdaXVvLINYXTI2GNqzaMuvacNx4uJQ8+b3zXR0pkgQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/plist/-/plist-3.1.0.tgz}
    name: plist
    version: 3.1.0
    engines: {node: '>=10.4.0'}
    dependencies:
      '@xmldom/xmldom': mirrors.tencent.com/@xmldom/xmldom/0.8.10
      base64-js: mirrors.tencent.com/base64-js/1.5.1
      xmlbuilder: mirrors.tencent.com/xmlbuilder/15.1.1
    dev: true

  mirrors.tencent.com/postcss/8.5.4:
    resolution: {integrity: sha512-QSa9EBe+uwlGTFmHsPKokv3B/oEMQZxfqW0QqNCyhpa6mB1afzulwn8hihglqAb2pOw+BJgNlmXQ8la2VeHB7w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/postcss/-/postcss-8.5.4.tgz}
    name: postcss
    version: 8.5.4
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: mirrors.tencent.com/nanoid/3.3.11
      picocolors: mirrors.tencent.com/picocolors/1.1.1
      source-map-js: mirrors.tencent.com/source-map-js/1.2.1
    dev: true

  mirrors.tencent.com/prelude-ls/1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/prelude-ls/-/prelude-ls-1.2.1.tgz}
    name: prelude-ls
    version: 1.2.1
    engines: {node: '>= 0.8.0'}
    dev: true

  mirrors.tencent.com/progress/2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/progress/-/progress-2.0.3.tgz}
    name: progress
    version: 2.0.3
    engines: {node: '>=0.4.0'}
    dev: false

  mirrors.tencent.com/promise-retry/2.0.1:
    resolution: {integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/promise-retry/-/promise-retry-2.0.1.tgz}
    name: promise-retry
    version: 2.0.1
    engines: {node: '>=10'}
    dependencies:
      err-code: mirrors.tencent.com/err-code/2.0.3
      retry: mirrors.tencent.com/retry/0.12.0
    dev: true

  mirrors.tencent.com/prop-types/15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/prop-types/-/prop-types-15.8.1.tgz}
    name: prop-types
    version: 15.8.1
    dependencies:
      loose-envify: mirrors.tencent.com/loose-envify/1.4.0
      object-assign: mirrors.tencent.com/object-assign/4.1.1
      react-is: mirrors.tencent.com/react-is/16.13.1
    dev: true

  mirrors.tencent.com/proxy-from-env/1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/proxy-from-env/-/proxy-from-env-1.1.0.tgz}
    name: proxy-from-env
    version: 1.1.0
    dev: false

  mirrors.tencent.com/psl/1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/psl/-/psl-1.15.0.tgz}
    name: psl
    version: 1.15.0
    dependencies:
      punycode: mirrors.tencent.com/punycode/2.3.1
    dev: false

  mirrors.tencent.com/pump/3.0.2:
    resolution: {integrity: sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/pump/-/pump-3.0.2.tgz}
    name: pump
    version: 3.0.2
    dependencies:
      end-of-stream: mirrors.tencent.com/end-of-stream/1.4.4
      once: mirrors.tencent.com/once/1.4.0
    dev: false

  mirrors.tencent.com/punycode/2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/punycode/-/punycode-2.3.1.tgz}
    name: punycode
    version: 2.3.1
    engines: {node: '>=6'}

  mirrors.tencent.com/qs/6.5.3:
    resolution: {integrity: sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/qs/-/qs-6.5.3.tgz}
    name: qs
    version: 6.5.3
    engines: {node: '>=0.6'}
    dev: false

  mirrors.tencent.com/quansync/0.2.10:
    resolution: {integrity: sha512-t41VRkMYbkHyCYmOvx/6URnN80H7k4X0lLdBMGsz+maAwrJQYB1djpV6vHrQIBE0WBSGqhtEHrK9U3DWWH8v7A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/quansync/-/quansync-0.2.10.tgz}
    name: quansync
    version: 0.2.10
    dev: true

  mirrors.tencent.com/queue-microtask/1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  mirrors.tencent.com/quick-lru/5.1.1:
    resolution: {integrity: sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/quick-lru/-/quick-lru-5.1.1.tgz}
    name: quick-lru
    version: 5.1.1
    engines: {node: '>=10'}
    dev: false

  mirrors.tencent.com/raf/3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/raf/-/raf-3.4.1.tgz}
    name: raf
    version: 3.4.1
    dependencies:
      performance-now: mirrors.tencent.com/performance-now/2.1.0
    dev: true

  mirrors.tencent.com/react-dom/18.3.1_react@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react-dom/-/react-dom-18.3.1.tgz}
    id: mirrors.tencent.com/react-dom/18.3.1
    name: react-dom
    version: 18.3.1
    peerDependencies:
      react: ^18.3.1
    dependencies:
      loose-envify: mirrors.tencent.com/loose-envify/1.4.0
      react: mirrors.tencent.com/react/18.3.1
      scheduler: mirrors.tencent.com/scheduler/0.23.2
    dev: true

  mirrors.tencent.com/react-fast-compare/3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react-fast-compare/-/react-fast-compare-3.2.2.tgz}
    name: react-fast-compare
    version: 3.2.2
    dev: true

  mirrors.tencent.com/react-is/16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react-is/-/react-is-16.13.1.tgz}
    name: react-is
    version: 16.13.1
    dev: true

  mirrors.tencent.com/react-is/18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react-is/-/react-is-18.3.1.tgz}
    name: react-is
    version: 18.3.1
    dev: true

  mirrors.tencent.com/react-transition-group/4.4.5_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react-transition-group/-/react-transition-group-4.4.5.tgz}
    id: mirrors.tencent.com/react-transition-group/4.4.5
    name: react-transition-group
    version: 4.4.5
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.26.10
      dom-helpers: mirrors.tencent.com/dom-helpers/5.2.1
      loose-envify: mirrors.tencent.com/loose-envify/1.4.0
      prop-types: mirrors.tencent.com/prop-types/15.8.1
      react: mirrors.tencent.com/react/18.3.1
      react-dom: mirrors.tencent.com/react-dom/18.3.1_react@18.3.1
    dev: true

  mirrors.tencent.com/react/18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/react/-/react-18.3.1.tgz}
    name: react
    version: 18.3.1
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: mirrors.tencent.com/loose-envify/1.4.0
    dev: true

  mirrors.tencent.com/read-config-file/6.3.2:
    resolution: {integrity: sha512-M80lpCjnE6Wt6zb98DoW8WHR09nzMSpu8XHtPkiTHrJ5Az9CybfeQhTJ8D7saeBHpGhLPIVyA8lcL6ZmdKwY6Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/read-config-file/-/read-config-file-6.3.2.tgz}
    name: read-config-file
    version: 6.3.2
    engines: {node: '>=12.0.0'}
    dependencies:
      config-file-ts: mirrors.tencent.com/config-file-ts/0.2.6
      dotenv: mirrors.tencent.com/dotenv/9.0.2
      dotenv-expand: mirrors.tencent.com/dotenv-expand/5.1.0
      js-yaml: mirrors.tencent.com/js-yaml/4.1.0
      json5: mirrors.tencent.com/json5/2.2.3
      lazy-val: mirrors.tencent.com/lazy-val/1.0.5
    dev: true

  mirrors.tencent.com/regenerator-runtime/0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz}
    name: regenerator-runtime
    version: 0.14.1
    dev: true

  mirrors.tencent.com/request/2.88.2:
    resolution: {integrity: sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/request/-/request-2.88.2.tgz}
    name: request
    version: 2.88.2
    engines: {node: '>= 6'}
    deprecated: request has been deprecated, see https://github.com/request/request/issues/3142
    dependencies:
      aws-sign2: mirrors.tencent.com/aws-sign2/0.7.0
      aws4: mirrors.tencent.com/aws4/1.13.2
      caseless: mirrors.tencent.com/caseless/0.12.0
      combined-stream: mirrors.tencent.com/combined-stream/1.0.8
      extend: mirrors.tencent.com/extend/3.0.2
      forever-agent: mirrors.tencent.com/forever-agent/0.6.1
      form-data: mirrors.tencent.com/form-data/2.3.3
      har-validator: mirrors.tencent.com/har-validator/5.1.5
      http-signature: mirrors.tencent.com/http-signature/1.2.0
      is-typedarray: mirrors.tencent.com/is-typedarray/1.0.0
      isstream: mirrors.tencent.com/isstream/0.1.2
      json-stringify-safe: mirrors.tencent.com/json-stringify-safe/5.0.1
      mime-types: mirrors.tencent.com/mime-types/2.1.35
      oauth-sign: mirrors.tencent.com/oauth-sign/0.9.0
      performance-now: mirrors.tencent.com/performance-now/2.1.0
      qs: mirrors.tencent.com/qs/6.5.3
      safe-buffer: mirrors.tencent.com/safe-buffer/5.2.1
      tough-cookie: mirrors.tencent.com/tough-cookie/2.5.0
      tunnel-agent: mirrors.tencent.com/tunnel-agent/0.6.0
      uuid: mirrors.tencent.com/uuid/3.4.0
    dev: false

  mirrors.tencent.com/require-directory/2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/require-directory/-/require-directory-2.1.1.tgz}
    name: require-directory
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/require-from-string/2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/require-from-string/-/require-from-string-2.0.2.tgz}
    name: require-from-string
    version: 2.0.2
    engines: {node: '>=0.10.0'}

  mirrors.tencent.com/resize-observer-polyfill/1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz}
    name: resize-observer-polyfill
    version: 1.5.1
    dev: true

  mirrors.tencent.com/resolve-alpn/1.2.1:
    resolution: {integrity: sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/resolve-alpn/-/resolve-alpn-1.2.1.tgz}
    name: resolve-alpn
    version: 1.2.1
    dev: false

  mirrors.tencent.com/resolve-from/4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/resolve-from/-/resolve-from-4.0.0.tgz}
    name: resolve-from
    version: 4.0.0
    engines: {node: '>=4'}
    dev: true

  mirrors.tencent.com/resolve/1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/resolve/-/resolve-1.22.10.tgz}
    name: resolve
    version: 1.22.10
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: mirrors.tencent.com/is-core-module/2.16.1
      path-parse: mirrors.tencent.com/path-parse/1.0.7
      supports-preserve-symlinks-flag: mirrors.tencent.com/supports-preserve-symlinks-flag/1.0.0
    dev: true

  mirrors.tencent.com/responselike/2.0.1:
    resolution: {integrity: sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/responselike/-/responselike-2.0.1.tgz}
    name: responselike
    version: 2.0.1
    dependencies:
      lowercase-keys: mirrors.tencent.com/lowercase-keys/2.0.0
    dev: false

  mirrors.tencent.com/retry/0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/retry/-/retry-0.12.0.tgz}
    name: retry
    version: 0.12.0
    engines: {node: '>= 4'}
    dev: true

  mirrors.tencent.com/reusify/1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/reusify/-/reusify-1.1.0.tgz}
    name: reusify
    version: 1.1.0
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/rimraf/3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/rimraf/-/rimraf-3.0.2.tgz}
    name: rimraf
    version: 3.0.2
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: mirrors.tencent.com/glob/7.2.3
    dev: true

  mirrors.tencent.com/roarr/2.15.4:
    resolution: {integrity: sha512-CHhPh+UNHD2GTXNYhPWLnU8ONHdI+5DI+4EYIAOaiD63rHeYlZvyh8P+in5999TTSFgUYuKUAjzRI4mdh/p+2A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/roarr/-/roarr-2.15.4.tgz}
    name: roarr
    version: 2.15.4
    engines: {node: '>=8.0'}
    dependencies:
      boolean: mirrors.tencent.com/boolean/3.2.0
      detect-node: mirrors.tencent.com/detect-node/2.1.0
      globalthis: mirrors.tencent.com/globalthis/1.0.4
      json-stringify-safe: mirrors.tencent.com/json-stringify-safe/5.0.1
      semver-compare: mirrors.tencent.com/semver-compare/1.0.0
      sprintf-js: mirrors.tencent.com/sprintf-js/1.1.3
    dev: false
    optional: true

  mirrors.tencent.com/rollup/4.42.0:
    resolution: {integrity: sha512-LW+Vse3BJPyGJGAJt1j8pWDKPd73QM8cRXYK1IxOBgL2AGLu7Xd2YOW0M2sLUBCkF5MshXXtMApyEAEzMVMsnw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/rollup/-/rollup-4.42.0.tgz}
    name: rollup
    version: 4.42.0
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': mirrors.tencent.com/@types/estree/1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': mirrors.tencent.com/@rollup/rollup-android-arm-eabi/4.42.0
      '@rollup/rollup-android-arm64': mirrors.tencent.com/@rollup/rollup-android-arm64/4.42.0
      '@rollup/rollup-darwin-arm64': mirrors.tencent.com/@rollup/rollup-darwin-arm64/4.42.0
      '@rollup/rollup-darwin-x64': mirrors.tencent.com/@rollup/rollup-darwin-x64/4.42.0
      '@rollup/rollup-freebsd-arm64': mirrors.tencent.com/@rollup/rollup-freebsd-arm64/4.42.0
      '@rollup/rollup-freebsd-x64': mirrors.tencent.com/@rollup/rollup-freebsd-x64/4.42.0
      '@rollup/rollup-linux-arm-gnueabihf': mirrors.tencent.com/@rollup/rollup-linux-arm-gnueabihf/4.42.0
      '@rollup/rollup-linux-arm-musleabihf': mirrors.tencent.com/@rollup/rollup-linux-arm-musleabihf/4.42.0
      '@rollup/rollup-linux-arm64-gnu': mirrors.tencent.com/@rollup/rollup-linux-arm64-gnu/4.42.0
      '@rollup/rollup-linux-arm64-musl': mirrors.tencent.com/@rollup/rollup-linux-arm64-musl/4.42.0
      '@rollup/rollup-linux-loongarch64-gnu': mirrors.tencent.com/@rollup/rollup-linux-loongarch64-gnu/4.42.0
      '@rollup/rollup-linux-powerpc64le-gnu': mirrors.tencent.com/@rollup/rollup-linux-powerpc64le-gnu/4.42.0
      '@rollup/rollup-linux-riscv64-gnu': mirrors.tencent.com/@rollup/rollup-linux-riscv64-gnu/4.42.0
      '@rollup/rollup-linux-riscv64-musl': mirrors.tencent.com/@rollup/rollup-linux-riscv64-musl/4.42.0
      '@rollup/rollup-linux-s390x-gnu': mirrors.tencent.com/@rollup/rollup-linux-s390x-gnu/4.42.0
      '@rollup/rollup-linux-x64-gnu': mirrors.tencent.com/@rollup/rollup-linux-x64-gnu/4.42.0
      '@rollup/rollup-linux-x64-musl': mirrors.tencent.com/@rollup/rollup-linux-x64-musl/4.42.0
      '@rollup/rollup-win32-arm64-msvc': mirrors.tencent.com/@rollup/rollup-win32-arm64-msvc/4.42.0
      '@rollup/rollup-win32-ia32-msvc': mirrors.tencent.com/@rollup/rollup-win32-ia32-msvc/4.42.0
      '@rollup/rollup-win32-x64-msvc': mirrors.tencent.com/@rollup/rollup-win32-x64-msvc/4.42.0
      fsevents: mirrors.tencent.com/fsevents/2.3.3
    dev: true

  mirrors.tencent.com/run-parallel/1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: mirrors.tencent.com/queue-microtask/1.2.3
    dev: true

  mirrors.tencent.com/safe-buffer/5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/safe-buffer/-/safe-buffer-5.2.1.tgz}
    name: safe-buffer
    version: 5.2.1
    dev: false

  mirrors.tencent.com/safer-buffer/2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/safer-buffer/-/safer-buffer-2.1.2.tgz}
    name: safer-buffer
    version: 2.1.2

  mirrors.tencent.com/sanitize-filename/1.6.3:
    resolution: {integrity: sha512-y/52Mcy7aw3gRm7IrcGDFx/bCk4AhRh2eI9luHOQM86nZsqwiRkkq2GekHXBBD+SmPidc8i2PqtYZl+pWJ8Oeg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sanitize-filename/-/sanitize-filename-1.6.3.tgz}
    name: sanitize-filename
    version: 1.6.3
    dependencies:
      truncate-utf8-bytes: mirrors.tencent.com/truncate-utf8-bytes/1.0.2
    dev: true

  mirrors.tencent.com/sax/1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sax/-/sax-1.4.1.tgz}
    name: sax
    version: 1.4.1
    dev: true

  mirrors.tencent.com/scheduler/0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/scheduler/-/scheduler-0.23.2.tgz}
    name: scheduler
    version: 0.23.2
    dependencies:
      loose-envify: mirrors.tencent.com/loose-envify/1.4.0
    dev: true

  mirrors.tencent.com/screenfull/5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/screenfull/-/screenfull-5.2.0.tgz}
    name: screenfull
    version: 5.2.0
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/semver-compare/1.0.0:
    resolution: {integrity: sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/semver-compare/-/semver-compare-1.0.0.tgz}
    name: semver-compare
    version: 1.0.0
    dev: false
    optional: true

  mirrors.tencent.com/semver/6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/semver/-/semver-6.3.1.tgz}
    name: semver
    version: 6.3.1
    hasBin: true
    dev: false

  mirrors.tencent.com/semver/7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/semver/-/semver-7.5.4.tgz}
    name: semver
    version: 7.5.4
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: mirrors.tencent.com/lru-cache/6.0.0
    dev: true

  mirrors.tencent.com/semver/7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/semver/-/semver-7.7.2.tgz}
    name: semver
    version: 7.7.2
    engines: {node: '>=10'}
    hasBin: true

  mirrors.tencent.com/serialize-error/7.0.1:
    resolution: {integrity: sha512-8I8TjW5KMOKsZQTvoxjuSIa7foAwPWGOts+6o7sgjz41/qMD9VQHEDxi6PBvK2l0MXUmqZyNpUK+T2tQaaElvw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/serialize-error/-/serialize-error-7.0.1.tgz}
    name: serialize-error
    version: 7.0.1
    engines: {node: '>=10'}
    dependencies:
      type-fest: mirrors.tencent.com/type-fest/0.13.1
    dev: false
    optional: true

  mirrors.tencent.com/shebang-command/2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: mirrors.tencent.com/shebang-regex/3.0.0
    dev: true

  mirrors.tencent.com/shebang-regex/3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/signal-exit/4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/signal-exit/-/signal-exit-4.1.0.tgz}
    name: signal-exit
    version: 4.1.0
    engines: {node: '>=14'}
    dev: true

  mirrors.tencent.com/simple-update-notifier/2.0.0:
    resolution: {integrity: sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz}
    name: simple-update-notifier
    version: 2.0.0
    engines: {node: '>=10'}
    dependencies:
      semver: mirrors.tencent.com/semver/7.7.2
    dev: true

  mirrors.tencent.com/slash/3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/slash/-/slash-3.0.0.tgz}
    name: slash
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/slice-ansi/3.0.0:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/slice-ansi/-/slice-ansi-3.0.0.tgz}
    name: slice-ansi
    version: 3.0.0
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: mirrors.tencent.com/ansi-styles/4.3.0
      astral-regex: mirrors.tencent.com/astral-regex/2.0.0
      is-fullwidth-code-point: mirrors.tencent.com/is-fullwidth-code-point/3.0.0
    dev: true
    optional: true

  mirrors.tencent.com/smart-buffer/4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/smart-buffer/-/smart-buffer-4.2.0.tgz}
    name: smart-buffer
    version: 4.2.0
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}
    dev: true
    optional: true

  mirrors.tencent.com/sortablejs/1.15.6:
    resolution: {integrity: sha512-aNfiuwMEpfBM/CN6LY0ibyhxPfPbyFeBTYJKCvzkJ2GkUpazIt3H+QIPAMHwqQ7tMKaHz1Qj+rJJCqljnf4p3A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sortablejs/-/sortablejs-1.15.6.tgz}
    name: sortablejs
    version: 1.15.6
    dev: true

  mirrors.tencent.com/source-map-js/1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/source-map-js/-/source-map-js-1.2.1.tgz}
    name: source-map-js
    version: 1.2.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/source-map-support/0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/source-map-support/-/source-map-support-0.5.21.tgz}
    name: source-map-support
    version: 0.5.21
    dependencies:
      buffer-from: mirrors.tencent.com/buffer-from/1.1.2
      source-map: mirrors.tencent.com/source-map/0.6.1
    dev: true

  mirrors.tencent.com/source-map/0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/source-map/-/source-map-0.5.7.tgz}
    name: source-map
    version: 0.5.7
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/source-map/0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/sprintf-js/1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sprintf-js/-/sprintf-js-1.0.3.tgz}
    name: sprintf-js
    version: 1.0.3
    dev: true

  mirrors.tencent.com/sprintf-js/1.1.3:
    resolution: {integrity: sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sprintf-js/-/sprintf-js-1.1.3.tgz}
    name: sprintf-js
    version: 1.1.3
    dev: false
    optional: true

  mirrors.tencent.com/sshpk/1.18.0:
    resolution: {integrity: sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sshpk/-/sshpk-1.18.0.tgz}
    name: sshpk
    version: 1.18.0
    engines: {node: '>=0.10.0'}
    hasBin: true
    dependencies:
      asn1: mirrors.tencent.com/asn1/0.2.6
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
      bcrypt-pbkdf: mirrors.tencent.com/bcrypt-pbkdf/1.0.2
      dashdash: mirrors.tencent.com/dashdash/1.14.1
      ecc-jsbn: mirrors.tencent.com/ecc-jsbn/0.1.2
      getpass: mirrors.tencent.com/getpass/0.1.7
      jsbn: mirrors.tencent.com/jsbn/0.1.1
      safer-buffer: mirrors.tencent.com/safer-buffer/2.1.2
      tweetnacl: mirrors.tencent.com/tweetnacl/0.14.5
    dev: false

  mirrors.tencent.com/stat-mode/1.0.0:
    resolution: {integrity: sha512-jH9EhtKIjuXZ2cWxmXS8ZP80XyC3iasQxMDV8jzhNJpfDb7VbQLVW4Wvsxz9QZvzV+G4YoSfBUVKDOyxLzi/sg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/stat-mode/-/stat-mode-1.0.0.tgz}
    name: stat-mode
    version: 1.0.0
    engines: {node: '>= 6'}
    dev: true

  mirrors.tencent.com/string-argv/0.3.2:
    resolution: {integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/string-argv/-/string-argv-0.3.2.tgz}
    name: string-argv
    version: 0.3.2
    engines: {node: '>=0.6.19'}
    dev: true

  mirrors.tencent.com/string-width/4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/string-width/-/string-width-4.2.3.tgz}
    name: string-width
    version: 4.2.3
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: mirrors.tencent.com/emoji-regex/8.0.0
      is-fullwidth-code-point: mirrors.tencent.com/is-fullwidth-code-point/3.0.0
      strip-ansi: mirrors.tencent.com/strip-ansi/6.0.1
    dev: true

  mirrors.tencent.com/string-width/5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/string-width/-/string-width-5.1.2.tgz}
    name: string-width
    version: 5.1.2
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: mirrors.tencent.com/eastasianwidth/0.2.0
      emoji-regex: mirrors.tencent.com/emoji-regex/9.2.2
      strip-ansi: mirrors.tencent.com/strip-ansi/7.1.0
    dev: true

  mirrors.tencent.com/strip-ansi/6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/strip-ansi/-/strip-ansi-6.0.1.tgz}
    name: strip-ansi
    version: 6.0.1
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: mirrors.tencent.com/ansi-regex/5.0.1
    dev: true

  mirrors.tencent.com/strip-ansi/7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/strip-ansi/-/strip-ansi-7.1.0.tgz}
    name: strip-ansi
    version: 7.1.0
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: mirrors.tencent.com/ansi-regex/6.1.0
    dev: true

  mirrors.tencent.com/strip-json-comments/3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    name: strip-json-comments
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  mirrors.tencent.com/strnum/1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/strnum/-/strnum-1.1.2.tgz}
    name: strnum
    version: 1.1.2

  mirrors.tencent.com/stylis/4.2.0:
    resolution: {integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/stylis/-/stylis-4.2.0.tgz}
    name: stylis
    version: 4.2.0
    dev: true

  mirrors.tencent.com/sumchecker/3.0.1:
    resolution: {integrity: sha512-MvjXzkz/BOfyVDkG0oFOtBxHX2u3gKbMHIF/dXblZsgD3BWOFLmHovIpZY7BykJdAjcqRCBi1WYBNdEC9yI7vg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/sumchecker/-/sumchecker-3.0.1.tgz}
    name: sumchecker
    version: 3.0.1
    engines: {node: '>= 8.0'}
    dependencies:
      debug: mirrors.tencent.com/debug/4.4.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  mirrors.tencent.com/supports-color/7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: mirrors.tencent.com/has-flag/4.0.0
    dev: true

  mirrors.tencent.com/supports-color/8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/supports-color/-/supports-color-8.1.1.tgz}
    name: supports-color
    version: 8.1.1
    engines: {node: '>=10'}
    dependencies:
      has-flag: mirrors.tencent.com/has-flag/4.0.0
    dev: true

  mirrors.tencent.com/supports-preserve-symlinks-flag/1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz}
    name: supports-preserve-symlinks-flag
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dev: true

  mirrors.tencent.com/tar/6.2.1:
    resolution: {integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tar/-/tar-6.2.1.tgz}
    name: tar
    version: 6.2.1
    engines: {node: '>=10'}
    dependencies:
      chownr: mirrors.tencent.com/chownr/2.0.0
      fs-minipass: mirrors.tencent.com/fs-minipass/2.1.0
      minipass: mirrors.tencent.com/minipass/5.0.0
      minizlib: mirrors.tencent.com/minizlib/2.1.2
      mkdirp: mirrors.tencent.com/mkdirp/1.0.4
      yallist: mirrors.tencent.com/yallist/4.0.0
    dev: true

  mirrors.tencent.com/tdesign-icons-react/0.3.5_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-D2ynJjhnXXS/SYJg8mKrbkdVjaHmOtD1dRiR/6fbStm19YjNcuOkkO95wsQzSjKMOBXrOkdnqWEZJP1HBI+Q9w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tdesign-icons-react/-/tdesign-icons-react-0.3.5.tgz}
    id: mirrors.tencent.com/tdesign-icons-react/0.3.5
    name: tdesign-icons-react
    version: 0.3.5
    peerDependencies:
      react: '>=16.13.1'
      react-dom: '>=16.13.1'
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.27.6
      classnames: mirrors.tencent.com/classnames/2.5.1
      react: mirrors.tencent.com/react/18.3.1
      react-dom: mirrors.tencent.com/react-dom/18.3.1_react@18.3.1
    dev: true

  mirrors.tencent.com/tdesign-icons-react/0.5.0_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-Gpl1Kxkb8+YXYjMsSW5W3kWy/drVB/huFLIHWhmJdUnEwUAW0Il+nDyb/BsRi51jLnalBwjYbOELEggH2qp6FQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tdesign-icons-react/-/tdesign-icons-react-0.5.0.tgz}
    id: mirrors.tencent.com/tdesign-icons-react/0.5.0
    name: tdesign-icons-react
    version: 0.5.0
    peerDependencies:
      react: '>=16.13.1'
      react-dom: '>=16.13.1'
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.26.10
      classnames: mirrors.tencent.com/classnames/2.5.1
      react: mirrors.tencent.com/react/18.3.1
      react-dom: mirrors.tencent.com/react-dom/18.3.1_react@18.3.1
    dev: true

  mirrors.tencent.com/tdesign-react/1.12.2_nnrd3gsncyragczmpvfhocinkq:
    resolution: {integrity: sha512-9mqykVUMgbJC6aAPi8UfIOOJ6drexRG4V8epTD2iMwYq6dFydueob8MHz+jxCIUtin2XAXOuvJKt1eEReZQSUg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tdesign-react/-/tdesign-react-1.12.2.tgz}
    id: mirrors.tencent.com/tdesign-react/1.12.2
    name: tdesign-react
    version: 1.12.2
    peerDependencies:
      react: '>=16.13.1'
      react-dom: '>=16.13.1'
    dependencies:
      '@babel/runtime': mirrors.tencent.com/@babel/runtime/7.26.10
      '@popperjs/core': mirrors.tencent.com/@popperjs/core/2.11.8
      '@types/sortablejs': mirrors.tencent.com/@types/sortablejs/1.15.8
      '@types/tinycolor2': mirrors.tencent.com/@types/tinycolor2/1.4.6
      '@types/validator': mirrors.tencent.com/@types/validator/13.15.1
      classnames: mirrors.tencent.com/classnames/2.5.1
      dayjs: mirrors.tencent.com/dayjs/1.11.10
      hoist-non-react-statics: mirrors.tencent.com/hoist-non-react-statics/3.3.2
      lodash-es: mirrors.tencent.com/lodash-es/4.17.21
      mitt: mirrors.tencent.com/mitt/3.0.1
      raf: mirrors.tencent.com/raf/3.4.1
      react: mirrors.tencent.com/react/18.3.1
      react-dom: mirrors.tencent.com/react-dom/18.3.1_react@18.3.1
      react-fast-compare: mirrors.tencent.com/react-fast-compare/3.2.2
      react-is: mirrors.tencent.com/react-is/18.3.1
      react-transition-group: mirrors.tencent.com/react-transition-group/4.4.5_nnrd3gsncyragczmpvfhocinkq
      sortablejs: mirrors.tencent.com/sortablejs/1.15.6
      tdesign-icons-react: mirrors.tencent.com/tdesign-icons-react/0.5.0_nnrd3gsncyragczmpvfhocinkq
      tinycolor2: mirrors.tencent.com/tinycolor2/1.6.0
      tslib: mirrors.tencent.com/tslib/2.3.1
      validator: mirrors.tencent.com/validator/13.15.15
    dev: true

  mirrors.tencent.com/temp-file/3.4.0:
    resolution: {integrity: sha512-C5tjlC/HCtVUOi3KWVokd4vHVViOmGjtLwIh4MuzPo/nMYTV/p1urt3RnMz2IWXDdKEGJH3k5+KPxtqRsUYGtg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/temp-file/-/temp-file-3.4.0.tgz}
    name: temp-file
    version: 3.4.0
    dependencies:
      async-exit-hook: mirrors.tencent.com/async-exit-hook/2.0.1
      fs-extra: mirrors.tencent.com/fs-extra/10.1.0
    dev: true

  mirrors.tencent.com/text-table/0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/text-table/-/text-table-0.2.0.tgz}
    name: text-table
    version: 0.2.0
    dev: true

  mirrors.tencent.com/tinycolor2/1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tinycolor2/-/tinycolor2-1.6.0.tgz}
    name: tinycolor2
    version: 1.6.0
    dev: true

  mirrors.tencent.com/tmp-promise/3.0.3:
    resolution: {integrity: sha512-RwM7MoPojPxsOBYnyd2hy0bxtIlVrihNs9pj5SUvY8Zz1sQcQG2tG1hSr8PDxfgEB8RNKDhqbIlroIarSNDNsQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tmp-promise/-/tmp-promise-3.0.3.tgz}
    name: tmp-promise
    version: 3.0.3
    dependencies:
      tmp: mirrors.tencent.com/tmp/0.2.3
    dev: true

  mirrors.tencent.com/tmp/0.2.3:
    resolution: {integrity: sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tmp/-/tmp-0.2.3.tgz}
    name: tmp
    version: 0.2.3
    engines: {node: '>=14.14'}
    dev: true

  mirrors.tencent.com/to-regex-range/5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: mirrors.tencent.com/is-number/7.0.0
    dev: true

  mirrors.tencent.com/tough-cookie/2.5.0:
    resolution: {integrity: sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tough-cookie/-/tough-cookie-2.5.0.tgz}
    name: tough-cookie
    version: 2.5.0
    engines: {node: '>=0.8'}
    dependencies:
      psl: mirrors.tencent.com/psl/1.15.0
      punycode: mirrors.tencent.com/punycode/2.3.1
    dev: false

  mirrors.tencent.com/truncate-utf8-bytes/1.0.2:
    resolution: {integrity: sha512-95Pu1QXQvruGEhv62XCMO3Mm90GscOCClvrIUwCM0PYOXK3kaF3l3sIHxx71ThJfcbM2O5Au6SO3AWCSEfW4mQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/truncate-utf8-bytes/-/truncate-utf8-bytes-1.0.2.tgz}
    name: truncate-utf8-bytes
    version: 1.0.2
    dependencies:
      utf8-byte-length: mirrors.tencent.com/utf8-byte-length/1.0.5
    dev: true

  mirrors.tencent.com/ts-api-utils/1.4.3_typescript@5.8.3:
    resolution: {integrity: sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ts-api-utils/-/ts-api-utils-1.4.3.tgz}
    id: mirrors.tencent.com/ts-api-utils/1.4.3
    name: ts-api-utils
    version: 1.4.3
    engines: {node: '>=16'}
    peerDependencies:
      typescript: '>=4.2.0'
    dependencies:
      typescript: mirrors.tencent.com/typescript/5.8.3
    dev: true

  mirrors.tencent.com/tslib/2.3.1:
    resolution: {integrity: sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tslib/-/tslib-2.3.1.tgz}
    name: tslib
    version: 2.3.1
    dev: true

  mirrors.tencent.com/tslib/2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tslib/-/tslib-2.8.1.tgz}
    name: tslib
    version: 2.8.1
    dev: true

  mirrors.tencent.com/tunnel-agent/0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tunnel-agent/-/tunnel-agent-0.6.0.tgz}
    name: tunnel-agent
    version: 0.6.0
    dependencies:
      safe-buffer: mirrors.tencent.com/safe-buffer/5.2.1
    dev: false

  mirrors.tencent.com/tweetnacl/0.14.5:
    resolution: {integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/tweetnacl/-/tweetnacl-0.14.5.tgz}
    name: tweetnacl
    version: 0.14.5
    dev: false

  mirrors.tencent.com/type-check/0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/type-check/-/type-check-0.4.0.tgz}
    name: type-check
    version: 0.4.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: mirrors.tencent.com/prelude-ls/1.2.1
    dev: true

  mirrors.tencent.com/type-fest/0.13.1:
    resolution: {integrity: sha512-34R7HTnG0XIJcBSn5XhDd7nNFPRcXYRZrBB2O2jdKqYODldSzBAqzsWoZYYvduky73toYS/ESqxPvkDf/F0XMg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.13.1.tgz}
    name: type-fest
    version: 0.13.1
    engines: {node: '>=10'}
    dev: false
    optional: true

  mirrors.tencent.com/type-fest/0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/type-fest/-/type-fest-0.20.2.tgz}
    name: type-fest
    version: 0.20.2
    engines: {node: '>=10'}
    dev: true

  mirrors.tencent.com/typescript/5.8.2:
    resolution: {integrity: sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/typescript/-/typescript-5.8.2.tgz}
    name: typescript
    version: 5.8.2
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  mirrors.tencent.com/typescript/5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/typescript/-/typescript-5.8.3.tgz}
    name: typescript
    version: 5.8.3
    engines: {node: '>=14.17'}
    hasBin: true
    dev: true

  mirrors.tencent.com/ufo/1.6.1:
    resolution: {integrity: sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/ufo/-/ufo-1.6.1.tgz}
    name: ufo
    version: 1.6.1
    dev: true

  mirrors.tencent.com/undici-types/6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/undici-types/-/undici-types-6.21.0.tgz}
    name: undici-types
    version: 6.21.0

  mirrors.tencent.com/universalify/0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/universalify/-/universalify-0.1.2.tgz}
    name: universalify
    version: 0.1.2
    engines: {node: '>= 4.0.0'}
    dev: false

  mirrors.tencent.com/universalify/2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/universalify/-/universalify-2.0.1.tgz}
    name: universalify
    version: 2.0.1
    engines: {node: '>= 10.0.0'}
    dev: true

  mirrors.tencent.com/uri-js/4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/uri-js/-/uri-js-4.4.1.tgz}
    name: uri-js
    version: 4.4.1
    dependencies:
      punycode: mirrors.tencent.com/punycode/2.3.1

  mirrors.tencent.com/utf8-byte-length/1.0.5:
    resolution: {integrity: sha512-Xn0w3MtiQ6zoz2vFyUVruaCL53O/DwUvkEeOvj+uulMm0BkUGYWmBYVyElqZaSLhY6ZD0ulfU3aBra2aVT4xfA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/utf8-byte-length/-/utf8-byte-length-1.0.5.tgz}
    name: utf8-byte-length
    version: 1.0.5
    dev: true

  mirrors.tencent.com/uuid/3.4.0:
    resolution: {integrity: sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/uuid/-/uuid-3.4.0.tgz}
    name: uuid
    version: 3.4.0
    deprecated: Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.
    hasBin: true
    dev: false

  mirrors.tencent.com/uzip/0.20201231.0:
    resolution: {integrity: sha512-OZeJfZP+R0z9D6TmBgLq2LHzSSptGMGDGigGiEe0pr8UBe/7fdflgHlHBNDASTXB5jnFuxHpNaJywSg8YFeGng==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/uzip/-/uzip-0.20201231.0.tgz}
    name: uzip
    version: 0.20201231.0
    dev: true

  mirrors.tencent.com/validator/13.15.15:
    resolution: {integrity: sha512-BgWVbCI72aIQy937xbawcs+hrVaN/CZ2UwutgaJ36hGqRrLNM+f5LUT/YPRbo8IV/ASeFzXszezV+y2+rq3l8A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/validator/-/validator-13.15.15.tgz}
    name: validator
    version: 13.15.15
    engines: {node: '>= 0.10'}
    dev: true

  mirrors.tencent.com/verror/1.10.0:
    resolution: {integrity: sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/verror/-/verror-1.10.0.tgz}
    name: verror
    version: 1.10.0
    engines: {'0': node >=0.6.0}
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
      core-util-is: mirrors.tencent.com/core-util-is/1.0.2
      extsprintf: mirrors.tencent.com/extsprintf/1.3.0
    dev: false

  mirrors.tencent.com/verror/1.10.1:
    resolution: {integrity: sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/verror/-/verror-1.10.1.tgz}
    name: verror
    version: 1.10.1
    engines: {node: '>=0.6.0'}
    dependencies:
      assert-plus: mirrors.tencent.com/assert-plus/1.0.0
      core-util-is: mirrors.tencent.com/core-util-is/1.0.2
      extsprintf: mirrors.tencent.com/extsprintf/1.4.1
    dev: true
    optional: true

  mirrors.tencent.com/vite-plugin-dts/4.5.4_q4274suvdejehmyjefaye234ii:
    resolution: {integrity: sha512-d4sOM8M/8z7vRXHHq/ebbblfaxENjogAAekcfcDCCwAyvGqnPrc7f4NZbvItS+g4WTgerW0xDwSz5qz11JT3vg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/vite-plugin-dts/-/vite-plugin-dts-4.5.4.tgz}
    id: mirrors.tencent.com/vite-plugin-dts/4.5.4
    name: vite-plugin-dts
    version: 4.5.4
    peerDependencies:
      typescript: '*'
      vite: '*'
    peerDependenciesMeta:
      vite:
        optional: true
    dependencies:
      '@microsoft/api-extractor': mirrors.tencent.com/@microsoft/api-extractor/7.52.8_@types+node@20.19.0
      '@rollup/pluginutils': mirrors.tencent.com/@rollup/pluginutils/5.1.4
      '@volar/typescript': mirrors.tencent.com/@volar/typescript/2.4.14
      '@vue/language-core': mirrors.tencent.com/@vue/language-core/2.2.0_typescript@5.8.3
      compare-versions: mirrors.tencent.com/compare-versions/6.1.1
      debug: mirrors.tencent.com/debug/4.4.1
      kolorist: mirrors.tencent.com/kolorist/1.8.0
      local-pkg: mirrors.tencent.com/local-pkg/1.1.1
      magic-string: mirrors.tencent.com/magic-string/0.30.17
      typescript: mirrors.tencent.com/typescript/5.8.3
      vite: mirrors.tencent.com/vite/5.4.19_@types+node@20.19.0
    transitivePeerDependencies:
      - '@types/node'
      - rollup
      - supports-color
    dev: true

  mirrors.tencent.com/vite-plugin-electron/0.28.8:
    resolution: {integrity: sha512-ir+B21oSGK9j23OEvt4EXyco9xDCaF6OGFe0V/8Zc0yL2+HMyQ6mmNQEIhXsEsZCSfIowBpwQBeHH4wVsfraeg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/vite-plugin-electron/-/vite-plugin-electron-0.28.8.tgz}
    name: vite-plugin-electron
    version: 0.28.8
    peerDependencies:
      vite-plugin-electron-renderer: '*'
    peerDependenciesMeta:
      vite-plugin-electron-renderer:
        optional: true
    dev: true

  mirrors.tencent.com/vite-plugin-no-bundle/4.0.0:
    resolution: {integrity: sha512-DXsJGXtp/QLWNFBfBqr+arxaTHEgiPCAgf9bcOPGv4n3AsFigsFj+oL95nFdMt8cRbgRDtvyTX802IZNBGg3Xg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/vite-plugin-no-bundle/-/vite-plugin-no-bundle-4.0.0.tgz}
    name: vite-plugin-no-bundle
    version: 4.0.0
    dependencies:
      fast-glob: mirrors.tencent.com/fast-glob/3.3.3
      micromatch: mirrors.tencent.com/micromatch/4.0.8
    dev: true

  mirrors.tencent.com/vite/5.4.19_@types+node@20.19.0:
    resolution: {integrity: sha512-qO3aKv3HoQC8QKiNSTuUM1l9o/XX3+c+VTgLHbJWHZGeTPVAg2XwazI9UWzoxjIJCGCV2zU60uqMzjeLZuULqA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/vite/-/vite-5.4.19.tgz}
    id: mirrors.tencent.com/vite/5.4.19
    name: vite
    version: 5.4.19
    engines: {node: ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || >=20.0.0
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      '@types/node': mirrors.tencent.com/@types/node/20.19.0
      esbuild: mirrors.tencent.com/esbuild/0.21.5
      postcss: mirrors.tencent.com/postcss/8.5.4
      rollup: mirrors.tencent.com/rollup/4.42.0
    optionalDependencies:
      fsevents: mirrors.tencent.com/fsevents/2.3.3
    dev: true

  mirrors.tencent.com/vscode-uri/3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/vscode-uri/-/vscode-uri-3.1.0.tgz}
    name: vscode-uri
    version: 3.1.0
    dev: true

  mirrors.tencent.com/which/2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: mirrors.tencent.com/isexe/2.0.0
    dev: true

  mirrors.tencent.com/word-wrap/1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/word-wrap/-/word-wrap-1.2.5.tgz}
    name: word-wrap
    version: 1.2.5
    engines: {node: '>=0.10.0'}
    dev: true

  mirrors.tencent.com/wrap-ansi/7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-7.0.0.tgz}
    name: wrap-ansi
    version: 7.0.0
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: mirrors.tencent.com/ansi-styles/4.3.0
      string-width: mirrors.tencent.com/string-width/4.2.3
      strip-ansi: mirrors.tencent.com/strip-ansi/6.0.1
    dev: true

  mirrors.tencent.com/wrap-ansi/8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/wrap-ansi/-/wrap-ansi-8.1.0.tgz}
    name: wrap-ansi
    version: 8.1.0
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: mirrors.tencent.com/ansi-styles/6.2.1
      string-width: mirrors.tencent.com/string-width/5.1.2
      strip-ansi: mirrors.tencent.com/strip-ansi/7.1.0
    dev: true

  mirrors.tencent.com/wrappy/1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/wrappy/-/wrappy-1.0.2.tgz}
    name: wrappy
    version: 1.0.2

  mirrors.tencent.com/xmlbuilder/15.1.1:
    resolution: {integrity: sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/xmlbuilder/-/xmlbuilder-15.1.1.tgz}
    name: xmlbuilder
    version: 15.1.1
    engines: {node: '>=8.0'}
    dev: true

  mirrors.tencent.com/y18n/5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/y18n/-/y18n-5.0.8.tgz}
    name: y18n
    version: 5.0.8
    engines: {node: '>=10'}
    dev: true

  mirrors.tencent.com/yallist/4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yallist/-/yallist-4.0.0.tgz}
    name: yallist
    version: 4.0.0
    dev: true

  mirrors.tencent.com/yaml/1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yaml/-/yaml-1.10.2.tgz}
    name: yaml
    version: 1.10.2
    engines: {node: '>= 6'}
    dev: true

  mirrors.tencent.com/yargs-parser/21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yargs-parser/-/yargs-parser-21.1.1.tgz}
    name: yargs-parser
    version: 21.1.1
    engines: {node: '>=12'}
    dev: true

  mirrors.tencent.com/yargs/17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yargs/-/yargs-17.7.2.tgz}
    name: yargs
    version: 17.7.2
    engines: {node: '>=12'}
    dependencies:
      cliui: mirrors.tencent.com/cliui/8.0.1
      escalade: mirrors.tencent.com/escalade/3.2.0
      get-caller-file: mirrors.tencent.com/get-caller-file/2.0.5
      require-directory: mirrors.tencent.com/require-directory/2.1.1
      string-width: mirrors.tencent.com/string-width/4.2.3
      y18n: mirrors.tencent.com/y18n/5.0.8
      yargs-parser: mirrors.tencent.com/yargs-parser/21.1.1
    dev: true

  mirrors.tencent.com/yauzl/2.10.0:
    resolution: {integrity: sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yauzl/-/yauzl-2.10.0.tgz}
    name: yauzl
    version: 2.10.0
    dependencies:
      buffer-crc32: mirrors.tencent.com/buffer-crc32/0.2.13
      fd-slicer: mirrors.tencent.com/fd-slicer/1.1.0
    dev: false

  mirrors.tencent.com/yocto-queue/0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==, registry: http://r.tnpm.oa.com/, tarball: https://mirrors.tencent.com/npm/yocto-queue/-/yocto-queue-0.1.0.tgz}
    name: yocto-queue
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true
