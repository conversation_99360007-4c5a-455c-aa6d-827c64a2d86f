{"name": "@tencent/pagedoo-upload", "version": "1.0.17", "main": "cjs", "module": "es", "bin": {"pagedoo-upload": "./run.js"}, "files": ["es", "cjs", "dist-electron", "dist"], "scripts": {"dev": "vite", "build": "vite build && npm run build:lib", "serve": "electron .", "start": "npm run build && electron .", "build:lib": "rm -rf es cjs && vite build --config vite.lib.config.mts", "run": "node run.js", "build:run": "npm run build && npm run run", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "dist": "electron-builder", "preinstall": "npx only-allow pnpm", "publish:pkg": "node publish.js"}, "dependencies": {"@electron/remote": "^2.1.2", "axios": "^1.8.4", "cos-nodejs-sdk-v5": "^2.14.7", "electron": "^30.1.0"}, "devDependencies": {"@emotion/react": "^11.11.3", "@types/node": "^20.14.9", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "ahooks": "^3.8.0", "browser-image-compression": "^2.0.2", "cos-js-sdk-v5": "^1.8.1", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "fast-glob": "^3.3.2", "filesize": "^10.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "semver": "^7.6.3", "tdesign-icons-react": "^0.3.2", "tdesign-react": "^1.7.6", "typescript": "^5.2.2", "vite": "^5.4.15", "vite-plugin-dts": "^4.4.0", "vite-plugin-electron": "^0.28.7", "vite-plugin-no-bundle": "^4.0.0"}, "build": {"appId": "tencent.pagedoo.upload", "productName": "页匠图片上传", "mac": {"category": "public.app-category.games"}, "directories": {"buildResources": "build", "output": "bin"}, "win": {"target": [{"target": "portable", "arch": ["x64"]}]}}}